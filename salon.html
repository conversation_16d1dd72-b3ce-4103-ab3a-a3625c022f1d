<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SaaSnext Glow Studio</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts - Inter -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome for icons (e.g., for testimonials or services) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* Custom CSS to override/extend Tailwind or for specific animations */
        :root {
            --background-color: #fef6f9;
            --primary-color: #c55301;
            --accent-color: #ffb6c1;
            --text-color: #292524;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            scroll-behavior: smooth;
        }

        /* Section slide-up animation */
        .section-hidden {
            opacity: 0;
            transform: translateY(50px);
            transition: opacity 0.8s ease-out, transform 0.8s ease-out;
        }

        .section-visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Glow effect for cards/buttons */
        .glow-effect {
            transition: all 0.3s ease;
            box-shadow: 0 0 0px rgba(0, 0, 0, 0); /* Initial state */
        }

        .glow-effect:hover {
            box-shadow: 0 0 15px var(--accent-color); /* Glow on hover */
            transform: translateY(-5px);
        }

        /* Button specific styles for glow */
        .btn-glow {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }
        .btn-glow::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, var(--accent-color) 0%, transparent 80%);
            border-radius: 50%;
            transition: width 0.5s ease, height 0.5s ease, top 0.5s ease, left 0.5s ease, opacity 0.5s ease;
            opacity: 0;
            transform: translate(-50%, -50%);
            z-index: -1;
        }
        .btn-glow:hover::before {
            width: 200%;
            height: 200%;
            opacity: 0.7;
        }

        /* Booking Form Validation Animations */
        .input-error {
            border-color: #ef4444; /* Tailwind red-500 */
            box-shadow: 0 0 5px #ef4444;
            animation: shake 0.3s;
        }

        .error-message {
            color: #ef4444;
            font-size: 0.875rem; /* Tailwind text-sm */
            margin-top: 0.25rem;
            opacity: 0;
            height: 0;
            overflow: hidden;
            transition: opacity 0.3s ease-out, height 0.3s ease-out;
        }
        .error-message.show {
            opacity: 1;
            height: auto;
        }

        @keyframes shake {
            0% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            50% { transform: translateX(5px); }
            75% { transform: translateX(-5px); }
            100% { transform: translateX(0); }
        }

        /* Modal styling */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease-out, visibility 0.3s ease-out;
        }
        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }
        .modal-content {
            background-color: var(--background-color);
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            max-width: 90%;
            width: 500px;
            transform: scale(0.9);
            transition: transform 0.3s ease-out;
        }
        .modal-overlay.show .modal-content {
            transform: scale(1);
        }

        /* Testimonials slider styles */
        .slider-container {
            overflow: hidden;
            width: 100%;
        }
        .slider-wrapper {
            display: flex;
            transition: transform 0.5s ease-in-out;
        }
        .testimonial-slide {
            flex: 0 0 100%; /* Each slide takes full width */
            padding: 2rem;
            text-align: center;
            box-sizing: border-box;
        }
    </style>
</head>
<body class="selection:bg-accent-color selection:text-white">

    <!-- Navigation Bar -->
    <nav class="bg-primary-color p-4 shadow-lg fixed w-full z-50 rounded-b-xl">
        <div class="container mx-auto flex justify-between items-center px-4 md:px-0">
            <a href="#" class="text-white text-3xl font-bold tracking-wider rounded-lg p-2 transition-transform duration-300 hover:scale-105">SaaSnext Glow Studio</a>
            <ul class="hidden md:flex space-x-6">
                <li><a href="#hero" class="text-white text-lg font-medium hover:text-accent-color transition duration-300">Home</a></li>
                <li><a href="#services" class="text-white text-lg font-medium hover:text-accent-color transition duration-300">Services</a></li>
                <li><a href="#prices" class="text-white text-lg font-medium hover:text-accent-color transition duration-300">Prices</a></li>
                <li><a href="#testimonials" class="text-white text-lg font-medium hover:text-accent-color transition duration-300">Testimonials</a></li>
                <li><a href="#booking" class="text-white text-lg font-medium hover:text-accent-color transition duration-300">Book Now</a></li>
            </ul>
            <!-- Mobile Menu Button -->
            <button id="mobile-menu-button" class="md:hidden text-white focus:outline-none">
                <i class="fas fa-bars text-2xl"></i>
            </button>
        </div>
        <!-- Mobile Menu Overlay -->
        <div id="mobile-menu" class="fixed inset-0 bg-primary-color bg-opacity-95 hidden flex-col items-center justify-center space-y-8 z-40">
            <button id="close-mobile-menu" class="absolute top-6 right-6 text-white text-3xl focus:outline-none">
                <i class="fas fa-times"></i>
            </button>
            <ul class="text-center">
                <li><a href="#hero" class="text-white text-4xl font-bold py-4 block hover:text-accent-color transition duration-300" onclick="closeMobileMenu()">Home</a></li>
                <li><a href="#services" class="text-white text-4xl font-bold py-4 block hover:text-accent-color transition duration-300" onclick="closeMobileMenu()">Services</a></li>
                <li><a href="#prices" class="text-white text-4xl font-bold py-4 block hover:text-accent-color transition duration-300" onclick="closeMobileMenu()">Prices</a></li>
                <li><a href="#testimonials" class="text-white text-4xl font-bold py-4 block hover:text-accent-color transition duration-300" onclick="closeMobileMenu()">Testimonials</a></li>
                <li><a href="#booking" class="text-white text-4xl font-bold py-4 block hover:text-accent-color transition duration-300" onclick="closeMobileMenu()">Book Now</a></li>
            </ul>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="hero" class="relative bg-gradient-to-br from-primary-color to-accent-color text-white flex items-center justify-center min-h-screen py-24 rounded-b-3xl overflow-hidden shadow-inner section-hidden pt-32">
        <div class="absolute inset-0 z-0 opacity-20" style="background-image: url('https://placehold.co/1920x1080/c55301/ffb6c1?text=Glow'); background-size: cover; background-position: center;"></div>
        <div class="container mx-auto text-center z-10 px-4">
            <h1 class="text-6xl md:text-8xl font-extrabold mb-6 leading-tight drop-shadow-lg animate-fade-in-down">
                Unleash Your Inner Radiance
            </h1>
            <p class="text-xl md:text-2xl mb-10 max-w-2xl mx-auto opacity-90 animate-fade-in-up">
                Experience unparalleled beauty services tailored to illuminate your unique glow.
            </p>
            <a href="#booking" class="inline-block bg-white text-primary-color px-10 py-4 rounded-full text-xl font-bold shadow-lg hover:shadow-xl btn-glow transition-all duration-300 transform hover:scale-105">
                Book Your Glow Session Today!
            </a>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-20 bg-background-color section-hidden px-4">
        <div class="container mx-auto">
            <h2 class="text-5xl font-bold text-center mb-16 text-primary-color">Our Services</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
                <!-- Service Card 1 -->
                <div class="bg-white rounded-2xl shadow-xl p-8 text-center glow-effect transform transition-transform duration-300">
                    <div class="text-5xl text-accent-color mb-6">
                        <i class="fas fa-cut"></i>
                    </div>
                    <h3 class="text-3xl font-semibold mb-4 text-primary-color">Hair Styling & Cuts</h3>
                    <p class="text-lg text-text-color leading-relaxed">
                        From classic cuts to modern trends, our stylists create the perfect look that suits your personality.
                    </p>
                </div>
                <!-- Service Card 2 -->
                <div class="bg-white rounded-2xl shadow-xl p-8 text-center glow-effect transform transition-transform duration-300">
                    <div class="text-5xl text-accent-color mb-6">
                        <i class="fas fa-spa"></i>
                    </div>
                    <h3 class="text-3xl font-semibold mb-4 text-primary-color">Rejuvenating Facials</h3>
                    <p class="text-lg text-text-color leading-relaxed">
                        Indulge in our luxurious facials designed to cleanse, nourish, and restore your skin's natural radiance.
                    </p>
                </div>
                <!-- Service Card 3 -->
                <div class="bg-white rounded-2xl shadow-xl p-8 text-center glow-effect transform transition-transform duration-300">
                    <div class="text-5xl text-accent-color mb-6">
                        <i class="fas fa-hand-sparkles"></i>
                    </div>
                    <h3 class="text-3xl font-semibold mb-4 text-primary-color">Manicures & Pedicures</h3>
                    <p class="text-lg text-text-color leading-relaxed">
                        Pamper your hands and feet with our exquisite nail treatments, leaving them polished and perfect.
                    </p>
                </div>
                <!-- Service Card 4 -->
                <div class="bg-white rounded-2xl shadow-xl p-8 text-center glow-effect transform transition-transform duration-300">
                    <div class="text-5xl text-accent-color mb-6">
                        <i class="fas fa-brush"></i>
                    </div>
                    <h3 class="text-3xl font-semibold mb-4 text-primary-color">Makeup Artistry</h3>
                    <p class="text-lg text-text-color leading-relaxed">
                        Whether for a special occasion or a daily glow, our makeup artists enhance your features beautifully.
                    </p>
                </div>
                <!-- Service Card 5 -->
                <div class="bg-white rounded-2xl shadow-xl p-8 text-center glow-effect transform transition-transform duration-300">
                    <div class="text-5xl text-accent-color mb-6">
                        <i class="fas fa-palette"></i>
                    </div>
                    <h3 class="text-3xl font-semibold mb-4 text-primary-color">Hair Coloring</h3>
                    <p class="text-lg text-text-color leading-relaxed">
                        Transform your look with vibrant and lasting hair colors, from highlights to full color changes.
                    </p>
                </div>
                <!-- Service Card 6 -->
                <div class="bg-white rounded-2xl shadow-xl p-8 text-center glow-effect transform transition-transform duration-300">
                    <div class="text-5xl text-accent-color mb-6">
                        <i class="fas fa-tint"></i>
                    </div>
                    <h3 class="text-3xl font-semibold mb-4 text-primary-color">Waxing & Threading</h3>
                    <p class="text-lg text-text-color leading-relaxed">
                        Achieve silky smooth skin with our gentle and effective hair removal services.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Price List Section -->
    <section id="prices" class="py-20 bg-accent-color bg-opacity-20 section-hidden px-4">
        <div class="container mx-auto">
            <h2 class="text-5xl font-bold text-center mb-16 text-primary-color">Price List</h2>
            <div class="max-w-3xl mx-auto space-y-6">
                <!-- Price Category 1 -->
                <div class="bg-white rounded-2xl shadow-xl overflow-hidden price-panel">
                    <button class="w-full text-left p-6 flex justify-between items-center cursor-pointer focus:outline-none bg-primary-color text-white text-3xl font-semibold rounded-t-2xl toggle-button">
                        Hair Services
                        <i class="fas fa-chevron-down transform transition-transform duration-300"></i>
                    </button>
                    <div class="price-content hidden p-6 border-t border-gray-200">
                        <ul class="space-y-4 text-lg">
                            <li class="flex justify-between items-center py-2 border-b border-gray-100">
                                <span>Haircut & Style</span>
                                <span class="font-medium text-primary-color">$50 - $80</span>
                            </li>
                            <li class="flex justify-between items-center py-2 border-b border-gray-100">
                                <span>Coloring (Full)</span>
                                <span class="font-medium text-primary-color">$120+</span>
                            </li>
                            <li class="flex justify-between items-center py-2 border-b border-gray-100">
                                <span>Highlights/Balayage</span>
                                <span class="font-medium text-primary-color">$150+</span>
                            </li>
                            <li class="flex justify-between items-center py-2">
                                <span>Blowout</span>
                                <span class="font-medium text-primary-color">$40</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Price Category 2 -->
                <div class="bg-white rounded-2xl shadow-xl overflow-hidden price-panel">
                    <button class="w-full text-left p-6 flex justify-between items-center cursor-pointer focus:outline-none bg-primary-color text-white text-3xl font-semibold rounded-t-2xl toggle-button">
                        Facial & Skin Care
                        <i class="fas fa-chevron-down transform transition-transform duration-300"></i>
                    </button>
                    <div class="price-content hidden p-6 border-t border-gray-200">
                        <ul class="space-y-4 text-lg">
                            <li class="flex justify-between items-center py-2 border-b border-gray-100">
                                <span>Classic Facial</span>
                                <span class="font-medium text-primary-color">$75</span>
                            </li>
                            <li class="flex justify-between items-center py-2 border-b border-gray-100">
                                <span>Anti-Aging Facial</span>
                                <span class="font-medium text-primary-color">$100</span>
                            </li>
                            <li class="flex justify-between items-center py-2">
                                <span>Deep Cleansing Facial</span>
                                <span class="font-medium text-primary-color">$90</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Price Category 3 -->
                <div class="bg-white rounded-2xl shadow-xl overflow-hidden price-panel">
                    <button class="w-full text-left p-6 flex justify-between items-center cursor-pointer focus:outline-none bg-primary-color text-white text-3xl font-semibold rounded-t-2xl toggle-button">
                        Nail & Waxing Services
                        <i class="fas fa-chevron-down transform transition-transform duration-300"></i>
                    </button>
                    <div class="price-content hidden p-6 border-t border-gray-200">
                        <ul class="space-y-4 text-lg">
                            <li class="flex justify-between items-center py-2 border-b border-gray-100">
                                <span>Manicure</span>
                                <span class="font-medium text-primary-color">$30</span>
                            </li>
                            <li class="flex justify-between items-center py-2 border-b border-gray-100">
                                <span>Pedicure</span>
                                <span class="font-medium text-primary-color">$45</span>
                            </li>
                            <li class="flex justify-between items-center py-2 border-b border-gray-100">
                                <span>Full Leg Wax</span>
                                <span class="font-medium text-primary-color">$60</span>
                            </li>
                            <li class="flex justify-between items-center py-2">
                                <span>Eyebrow Threading</span>
                                <span class="font-medium text-primary-color">$20</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="py-20 bg-background-color section-hidden px-4">
        <div class="container mx-auto">
            <h2 class="text-5xl font-bold text-center mb-16 text-primary-color">What Our Clients Say</h2>
            <div class="relative max-w-4xl mx-auto">
                <div class="slider-container rounded-2xl shadow-xl bg-white p-8">
                    <div class="slider-wrapper">
                        <!-- Testimonial 1 -->
                        <div class="testimonial-slide flex-shrink-0">
                            <p class="text-2xl italic mb-6 text-text-color leading-relaxed">
                                "SaaSnext Glow Studio transformed my hair! The stylists are incredibly talented and the atmosphere is so relaxing. I always leave feeling amazing."
                            </p>
                            <p class="text-lg font-semibold text-primary-color">- Emily R.</p>
                        </div>
                        <!-- Testimonial 2 -->
                        <div class="testimonial-slide flex-shrink-0">
                            <p class="text-2xl italic mb-6 text-text-color leading-relaxed">
                                "The best facial I've ever had! My skin feels incredibly soft and looks so much brighter. Highly recommend their skincare services."
                            </p>
                            <p class="text-lg font-semibold text-primary-color">- Sarah L.</p>
                        </div>
                        <!-- Testimonial 3 -->
                        <div class="testimonial-slide flex-shrink-0">
                            <p class="text-2xl italic mb-6 text-text-color leading-relaxed">
                                "A truly luxurious experience from start to finish. The attention to detail in their manicures and pedicures is unmatched. My go-to salon now!"
                            </p>
                            <p class="text-lg font-semibold text-primary-color">- Jessica M.</p>
                        </div>
                    </div>
                </div>
                <!-- Slider Navigation Buttons -->
                <button id="prev-testimonial" class="absolute top-1/2 left-0 -translate-x-1/2 -translate-y-1/2 bg-primary-color text-white p-4 rounded-full shadow-lg hover:bg-accent-color transition-colors duration-300">
                    <i class="fas fa-chevron-left text-2xl"></i>
                </button>
                <button id="next-testimonial" class="absolute top-1/2 right-0 translate-x-1/2 -translate-y-1/2 bg-primary-color text-white p-4 rounded-full shadow-lg hover:bg-accent-color transition-colors duration-300">
                    <i class="fas fa-chevron-right text-2xl"></i>
                </button>
            </div>
        </div>
    </section>

    <!-- Booking Form Section -->
    <section id="booking" class="py-20 bg-accent-color bg-opacity-20 section-hidden px-4">
        <div class="container mx-auto max-w-3xl">
            <h2 class="text-5xl font-bold text-center mb-16 text-primary-color">Book Your Appointment</h2>
            <form id="booking-form" class="bg-white rounded-2xl shadow-xl p-8 space-y-6">
                <!-- Name Field -->
                <div>
                    <label for="name" class="block text-text-color text-lg font-medium mb-2">Full Name</label>
                    <input type="text" id="name" name="name" class="w-full p-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-accent-color focus:border-transparent transition-all duration-300" placeholder="John Doe">
                    <p id="name-error" class="error-message"></p>
                </div>
                <!-- Email Field -->
                <div>
                    <label for="email" class="block text-text-color text-lg font-medium mb-2">Email</label>
                    <input type="email" id="email" name="email" class="w-full p-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-accent-color focus:border-transparent transition-all duration-300" placeholder="<EMAIL>">
                    <p id="email-error" class="error-message"></p>
                </div>
                <!-- Phone Field -->
                <div>
                    <label for="phone" class="block text-text-color text-lg font-medium mb-2">Phone Number</label>
                    <input type="tel" id="phone" name="phone" class="w-full p-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-accent-color focus:border-transparent transition-all duration-300" placeholder="+****************">
                    <p id="phone-error" class="error-message"></p>
                </div>
                <!-- Service Field -->
                <div>
                    <label for="service" class="block text-text-color text-lg font-medium mb-2">Select Service</label>
                    <select id="service" name="service" class="w-full p-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-accent-color focus:border-transparent transition-all duration-300 bg-white appearance-none pr-8">
                        <option value="">-- Choose a Service --</option>
                        <option value="haircut">Haircut & Styling</option>
                        <option value="facial">Rejuvenating Facial</option>
                        <option value="manicure">Manicure & Pedicure</option>
                        <option value="makeup">Makeup Artistry</option>
                        <option value="coloring">Hair Coloring</option>
                        <option value="waxing">Waxing & Threading</option>
                    </select>
                    <p id="service-error" class="error-message"></p>
                </div>
                <!-- Date Field -->
                <div>
                    <label for="date" class="block text-text-color text-lg font-medium mb-2">Preferred Date</label>
                    <input type="date" id="date" name="date" class="w-full p-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-accent-color focus:border-transparent transition-all duration-300">
                    <p id="date-error" class="error-message"></p>
                </div>
                <!-- Time Field -->
                <div>
                    <label for="time" class="block text-text-color text-lg font-medium mb-2">Preferred Time</label>
                    <input type="time" id="time" name="time" class="w-full p-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-accent-color focus:border-transparent transition-all duration-300">
                    <p id="time-error" class="error-message"></p>
                </div>
                <!-- Message Field -->
                <div>
                    <label for="message" class="block text-text-color text-lg font-medium mb-2">Additional Notes (Optional)</label>
                    <textarea id="message" name="message" rows="4" class="w-full p-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-accent-color focus:border-transparent transition-all duration-300" placeholder="Any specific requests or concerns..."></textarea>
                </div>
                <!-- Submit Button -->
                <button type="submit" class="w-full bg-primary-color text-white px-8 py-4 rounded-full text-xl font-bold shadow-lg hover:shadow-xl btn-glow transition-all duration-300 transform hover:scale-105">
                    Confirm Booking
                </button>
            </form>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-primary-color text-white py-12 rounded-t-3xl text-center px-4">
        <div class="container mx-auto">
            <p class="text-2xl font-semibold mb-4">SaaSnext Glow Studio</p>
            <p class="text-lg mb-2">123 Beauty Lane, Glamour City, GS 12345</p>
            <p class="text-lg mb-2">Phone: <a href="tel:+15551234567" class="hover:text-accent-color transition duration-300">+****************</a></p>
            <p class="text-lg mb-4">Email: <a href="mailto:<EMAIL>" class="hover:text-accent-color transition duration-300"><EMAIL></a></p>
            <div class="flex justify-center space-x-6 text-3xl">
                <a href="#" class="hover:text-accent-color transition duration-300"><i class="fab fa-facebook-f"></i></a>
                <a href="#" class="hover:text-accent-color transition duration-300"><i class="fab fa-instagram"></i></a>
                <a href="#" class="hover:text-accent-color transition duration-300"><i class="fab fa-twitter"></i></a>
            </div>
            <p class="mt-8 text-sm opacity-80">&copy; 2024 SaaSnext Glow Studio. All rights reserved.</p>
        </div>
    </footer>

    <!-- Booking Confirmation Modal -->
    <div id="booking-modal" class="modal-overlay">
        <div class="modal-content text-center">
            <h3 class="text-4xl font-bold text-primary-color mb-6">Booking Confirmed!</h3>
            <p class="text-xl text-text-color mb-8">Your appointment has been successfully scheduled. We look forward to seeing you!</p>
            <button id="close-modal" class="bg-primary-color text-white px-8 py-3 rounded-full text-lg font-bold hover:bg-accent-color transition-colors duration-300">
                Great!
            </button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Mobile Menu Toggle
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            const closeMobileMenuButton = document.getElementById('close-mobile-menu');

            mobileMenuButton.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
            });

            closeMobileMenuButton.addEventListener('click', () => {
                mobileMenu.classList.add('hidden');
            });

            // Function to close mobile menu (for nav links)
            window.closeMobileMenu = () => {
                mobileMenu.classList.add('hidden');
            };

            // Intersection Observer for slide-up content
            const sections = document.querySelectorAll('section');
            const revealSection = (entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('section-visible');
                        entry.target.classList.remove('section-hidden');
                        observer.unobserve(entry.target); // Stop observing once visible
                    }
                });
            };

            const sectionObserver = new IntersectionObserver(revealSection, {
                root: null, // Viewport
                threshold: 0.15 // When 15% of the section is visible
            });

            sections.forEach(section => {
                section.classList.add('section-hidden');
                sectionObserver.observe(section);
            });

            // Smooth scrolling for navigation links
            document.querySelectorAll('nav a').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });

            // Price List Toggle Functionality
            const toggleButtons = document.querySelectorAll('.toggle-button');

            toggleButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const priceContent = button.nextElementSibling;
                    const icon = button.querySelector('i');

                    // Toggle visibility of the content
                    priceContent.classList.toggle('hidden');

                    // Toggle icon rotation
                    icon.classList.toggle('fa-chevron-down');
                    icon.classList.toggle('fa-chevron-up');

                    // Optional: Close other open panels
                    toggleButtons.forEach(otherButton => {
                        if (otherButton !== button) {
                            const otherContent = otherButton.nextElementSibling;
                            const otherIcon = otherButton.querySelector('i');
                            if (!otherContent.classList.contains('hidden')) {
                                otherContent.classList.add('hidden');
                                otherIcon.classList.remove('fa-chevron-up');
                                otherIcon.classList.add('fa-chevron-down');
                            }
                        }
                    });
                });
            });

            // Testimonials Slider
            const sliderWrapper = document.querySelector('.slider-wrapper');
            const prevButton = document.getElementById('prev-testimonial');
            const nextButton = document.getElementById('next-testimonial');
            const slides = document.querySelectorAll('.testimonial-slide');
            let currentIndex = 0;

            const updateSlider = () => {
                sliderWrapper.style.transform = `translateX(-${currentIndex * 100}%)`;
            };

            prevButton.addEventListener('click', () => {
                currentIndex = (currentIndex === 0) ? slides.length - 1 : currentIndex - 1;
                updateSlider();
            });

            nextButton.addEventListener('click', () => {
                currentIndex = (currentIndex === slides.length - 1) ? 0 : currentIndex + 1;
                updateSlider();
            });

            // Automatic slide change
            let slideInterval = setInterval(() => {
                currentIndex = (currentIndex === slides.length - 1) ? 0 : currentIndex + 1;
                updateSlider();
            }, 5000); // Change slide every 5 seconds

            // Pause on hover
            sliderWrapper.addEventListener('mouseenter', () => clearInterval(slideInterval));
            sliderWrapper.addEventListener('mouseleave', () => {
                slideInterval = setInterval(() => {
                    currentIndex = (currentIndex === slides.length - 1) ? 0 : currentIndex + 1;
                    updateSlider();
                }, 5000);
            });


            // Booking Form Validation and Modal
            const bookingForm = document.getElementById('booking-form');
            const bookingModal = document.getElementById('booking-modal');
            const closeModalButton = document.getElementById('close-modal');

            const showError = (elementId, message) => {
                const inputElement = document.getElementById(elementId);
                const errorElement = document.getElementById(`${elementId}-error`);

                inputElement.classList.add('input-error');
                errorElement.textContent = message;
                errorElement.classList.add('show');
            };

            const clearError = (elementId) => {
                const inputElement = document.getElementById(elementId);
                const errorElement = document.getElementById(`${elementId}-error`);

                inputElement.classList.remove('input-error');
                errorElement.textContent = '';
                errorElement.classList.remove('show');
            };

            const validateForm = () => {
                let isValid = true;

                // Name validation
                const name = document.getElementById('name');
                if (name.value.trim() === '') {
                    showError('name', 'Full Name is required.');
                    isValid = false;
                } else {
                    clearError('name');
                }

                // Email validation
                const email = document.getElementById('email');
                const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (email.value.trim() === '') {
                    showError('email', 'Email is required.');
                    isValid = false;
                } else if (!emailPattern.test(email.value)) {
                    showError('email', 'Please enter a valid email address.');
                    isValid = false;
                } else {
                    clearError('email');
                }

                // Phone validation (optional but good to have a simple check)
                const phone = document.getElementById('phone');
                // A very basic phone number regex for demonstration
                const phonePattern = /^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$/im;
                if (phone.value.trim() === '') {
                    showError('phone', 'Phone number is required.');
                    isValid = false;
                } else if (!phonePattern.test(phone.value)) {
                    showError('phone', 'Please enter a valid phone number.');
                    isValid = false;
                } else {
                    clearError('phone');
                }

                // Service validation
                const service = document.getElementById('service');
                if (service.value === '') {
                    showError('service', 'Please select a service.');
                    isValid = false;
                } else {
                    clearError('service');
                }

                // Date validation
                const date = document.getElementById('date');
                const selectedDate = new Date(date.value);
                const today = new Date();
                today.setHours(0, 0, 0, 0); // Reset time for comparison

                if (date.value.trim() === '') {
                    showError('date', 'Preferred Date is required.');
                    isValid = false;
                } else if (selectedDate < today) {
                    showError('date', 'Date cannot be in the past.');
                    isValid = false;
                } else {
                    clearError('date');
                }

                // Time validation
                const time = document.getElementById('time');
                if (time.value.trim() === '') {
                    showError('time', 'Preferred Time is required.');
                    isValid = false;
                } else {
                    clearError('time');
                }

                return isValid;
            };

            bookingForm.addEventListener('submit', (e) => {
                e.preventDefault(); // Prevent default form submission

                if (validateForm()) {
                    // Form is valid, show confirmation modal
                    bookingModal.classList.add('show');
                    // Reset form after successful submission
                    bookingForm.reset();
                }
            });

            closeModalButton.addEventListener('click', () => {
                bookingModal.classList.remove('show');
            });

            // Close modal when clicking outside of it
            bookingModal.addEventListener('click', (e) => {
                if (e.target === bookingModal) {
                    bookingModal.classList.remove('show');
                }
            });
        });
    </script>
</body>
</html>
