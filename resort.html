<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SaaSnext Resort Stay - Luxurious Retreat</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- AOS Library for Animate On Scroll -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Corrected AOS script URL -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <!-- Google Fonts - Inter -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome for Icons (Amenities, Social Media) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            overflow-x: hidden; /* Prevent horizontal scroll */
        }

        /* Custom Parallax Effect for Hero Section */
        .hero-parallax {
            background-image: url('https://images.unsplash.com/photo-1571003123894-1f0594d2f6ed?q=80&w=2950&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'); /* Stock Image for Hero */
            background-size: cover;
            background-position: center center;
            background-attachment: fixed; /* This creates the parallax effect */
        }

        /* Custom styling for luxurious feel */
        .btn-primary {
            @apply bg-gradient-to-r from-teal-500 to-blue-600 text-white font-bold py-3 px-8 rounded-full shadow-lg hover:from-teal-600 hover:to-blue-700 transition-all duration-300 transform hover:scale-105;
        }

        .section-title {
            @apply text-4xl md:text-5xl font-extrabold text-blue-900 mb-12 relative;
        }

        .section-title::after {
            content: '';
            @apply block w-24 h-1 bg-teal-500 mx-auto mt-4 rounded-full;
        }

        /* Room Card Slide-in Animation */
        .room-card.hidden-left {
            opacity: 0;
            transform: translateX(-100px);
            transition: opacity 0.8s ease-out, transform 0.8s ease-out;
        }

        .room-card.visible {
            opacity: 1;
            transform: translateX(0);
        }

        /* Gallery hover effect */
        .gallery-item-overlay {
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        .gallery-item:hover .gallery-item-overlay {
            opacity: 1;
        }

        /* Hide scrollbar for a cleaner look */
        ::-webkit-scrollbar {
            width: 0px;
            background: transparent; /* make scrollbar transparent */
        }
        /* For Firefox */
        html {
            scrollbar-width: none;
        }
    </style>
</head>
<body class="text-gray-800">

    <!-- Header -->
    <header class="bg-white shadow-lg fixed top-0 left-0 w-full z-50">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="text-2xl font-bold text-blue-900">
                SaaSnext <span class="text-teal-500">Resort</span>
            </div>
            <nav>
                <ul class="flex space-x-6 md:space-x-10">
                    <li><a href="#hero" class="text-gray-700 hover:text-teal-600 font-semibold transition-colors duration-200">Home</a></li>
                    <li><a href="#room-types" class="text-gray-700 hover:text-teal-600 font-semibold transition-colors duration-200">Rooms</a></li>
                    <li><a href="#amenities" class="text-gray-700 hover:text-teal-600 font-semibold transition-colors duration-200">Amenities</a></li>
                    <li><a href="#gallery" class="text-gray-700 hover:text-teal-600 font-semibold transition-colors duration-200">Gallery</a></li>
                    <li><a href="#book-now" class="text-gray-700 hover:text-teal-600 font-semibold transition-colors duration-200">Book Now</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="hero" class="relative h-screen flex items-center justify-center text-center text-white hero-parallax pt-20"> <!-- Added pt-20 to account for fixed header -->
        <div class="absolute inset-0 bg-black opacity-50"></div> <!-- Overlay for better text readability -->
        <div class="z-10 p-4">
            <h1 class="text-5xl md:text-7xl font-bold mb-4 animate-fade-in-up" style="animation-delay: 0.5s;">Experience Unrivaled Luxury</h1>
            <p class="text-xl md:text-2xl mb-8 animate-fade-in-up" style="animation-delay: 1s;">Your perfect escape awaits at SaaSnext Resort.</p>
            <a href="#book-now" class="btn-primary animate-fade-in-up" style="animation-delay: 1.5s;">Book Your Stay Now</a>
        </div>
    </section>

    <!-- Room Types Section -->
    <section id="room-types" class="py-20 bg-gray-100">
        <div class="container mx-auto px-4">
            <h2 class="section-title text-center">Our Luxurious Rooms & Suites</h2>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-10 mt-16" id="room-cards-container">

                <!-- Room Card 1: Deluxe Room -->
                <div class="room-card hidden-left bg-white rounded-xl shadow-xl overflow-hidden transform hover:scale-105 transition-transform duration-300">
                    <div class="room-slider relative">
                        <img src="https://images.unsplash.com/photo-1540541338287-4d2102f7405e?q=80&w=2950&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" onerror="this.onerror=null;this.src='https://placehold.co/600x400/90EE90/006400?text=Deluxe+Room+1';" alt="Deluxe Room 1" class="w-full h-64 object-cover">
                        <img src="https://images.unsplash.com/photo-1596436889139-38507727e02e?q=80&w=2950&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" onerror="this.onerror=null;this.src='https://placehold.co/600x400/87CEEB/00008B?text=Deluxe+Room+2';" alt="Deluxe Room 2" class="w-full h-64 object-cover hidden">
                        <img src="https://images.unsplash.com/photo-1596395379100-fcf0b08064a3?q=80&w=2950&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" onerror="this.onerror=null;this.src='https://placehold.co/600x400/FFA07A/8B0000?text=Deluxe+Room+3';" alt="Deluxe Room 3" class="w-full h-64 object-cover hidden">
                        <div class="absolute bottom-2 right-2 flex space-x-1">
                            <button class="slider-dot w-3 h-3 bg-white opacity-70 rounded-full"></button>
                            <button class="slider-dot w-3 h-3 bg-gray-400 opacity-70 rounded-full"></button>
                            <button class="slider-dot w-3 h-3 bg-gray-400 opacity-70 rounded-full"></button>
                        </div>
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-blue-800 mb-3">Deluxe Room</h3>
                        <p class="text-gray-600 mb-4">Spacious and elegant, our Deluxe Rooms offer stunning views and modern amenities for a comfortable stay.</p>
                        <div class="flex justify-between items-center text-lg font-semibold text-teal-600">
                            <span>From $250/night</span>
                            <a href="#book-now" class="text-blue-600 hover:underline">View Details</a>
                        </div>
                    </div>
                </div>

                <!-- Room Card 2: Executive Suite -->
                <div class="room-card hidden-left bg-white rounded-xl shadow-xl overflow-hidden transform hover:scale-105 transition-transform duration-300">
                    <div class="room-slider relative">
                        <img src="https://images.unsplash.com/photo-1582236371720-cd37da084992?q=80&w=2950&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" onerror="this.onerror=null;this.src='https://placehold.co/600x400/DAA520/8B4513?text=Executive+Suite+1';" alt="Executive Suite 1" class="w-full h-64 object-cover">
                        <img src="https://images.unsplash.com/photo-1560936357-12cc93136531?q=80&w=2950&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" onerror="this.onerror=null;this.src='https://placehold.co/600x400/B0E0E6/4682B4?text=Executive+Suite+2';" alt="Executive Suite 2" class="w-full h-64 object-cover hidden">
                        <img src="https://images.unsplash.com/photo-1550596323-952467d55919?q=80&w=2950&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" onerror="this.onerror=null;this.src='https://placehold.co/600x400/9370DB/4B0082?text=Executive+Suite+3';" alt="Executive Suite 3" class="w-full h-64 object-cover hidden">
                        <div class="absolute bottom-2 right-2 flex space-x-1">
                            <button class="slider-dot w-3 h-3 bg-white opacity-70 rounded-full"></button>
                            <button class="slider-dot w-3 h-3 bg-gray-400 opacity-70 rounded-full"></button>
                            <button class="slider-dot w-3 h-3 bg-gray-400 opacity-70 rounded-full"></button>
                        </div>
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-blue-800 mb-3">Executive Suite</h3>
                        <p class="text-gray-600 mb-4">Indulge in our Executive Suites, offering expanded living areas, premium amenities, and breathtaking panoramas.</p>
                        <div class="flex justify-between items-center text-lg font-semibold text-teal-600">
                            <span>From $450/night</span>
                            <a href="#book-now" class="text-blue-600 hover:underline">View Details</a>
                        </div>
                    </div>
                </div>

                <!-- Room Card 3: Presidential Villa -->
                <div class="room-card hidden-left bg-white rounded-xl shadow-xl overflow-hidden transform hover:scale-105 transition-transform duration-300">
                    <div class="room-slider relative">
                        <img src="https://images.unsplash.com/photo-1579735919985-e2d93e25b184?q=80&w=2950&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" onerror="this.onerror=null;this.src='https://placehold.co/600x400/CD853F/8B4513?text=Presidential+Villa+1';" alt="Presidential Villa 1" class="w-full h-64 object-cover">
                        <img src="https://images.unsplash.com/photo-1549429158-b78be05c1d35?q=80&w=2950&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" onerror="this.onerror=null;this.src='https://placehold.co/600x400/A52A2A/8B0000?text=Presidential+Villa+2';" alt="Presidential Villa 2" class="w-full h-64 object-cover hidden">
                        <img src="https://images.unsplash.com/photo-1571731671549-b003a3d5b4a0?q=80&w=2950&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" onerror="this.onerror=null;this.src='https://placehold.co/600x400/FFD700/DAA520?text=Presidential+Villa+3';" alt="Presidential Villa 3" class="w-full h-64 object-cover hidden">
                        <div class="absolute bottom-2 right-2 flex space-x-1">
                            <button class="slider-dot w-3 h-3 bg-white opacity-70 rounded-full"></button>
                            <button class="slider-dot w-3 h-3 bg-gray-400 opacity-70 rounded-full"></button>
                            <button class="slider-dot w-3 h-3 bg-gray-400 opacity-70 rounded-full"></button>
                        </div>
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-blue-800 mb-3">Presidential Villa</h3>
                        <p class="text-gray-600 mb-4">The pinnacle of luxury. Our Presidential Villa offers unparalleled privacy, opulent design, and exclusive services.</p>
                        <div class="flex justify-between items-center text-lg font-semibold text-teal-600">
                            <span>From $999/night</span>
                            <a href="#book-now" class="text-blue-600 hover:underline">View Details</a>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section>

    <!-- Amenities Section -->
    <section id="amenities" class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <h2 class="section-title text-center">Resort Amenities</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 mt-16 text-center">

                <!-- Amenity Card 1: Infinity Pool -->
                <div class="p-6 rounded-xl shadow-lg bg-blue-50 hover:bg-blue-100 transition-colors duration-300" data-aos="fade-up" data-aos-delay="100">
                    <div class="text-5xl text-teal-500 mb-4"><i class="fas fa-swimming-pool"></i></div>
                    <h3 class="text-xl font-semibold text-blue-800 mb-2">Infinity Pool</h3>
                    <p class="text-gray-600">Dive into our stunning infinity pool with panoramic ocean views.</p>
                </div>

                <!-- Amenity Card 2: Fine Dining -->
                <div class="p-6 rounded-xl shadow-lg bg-blue-50 hover:bg-blue-100 transition-colors duration-300" data-aos="fade-up" data-aos-delay="200">
                    <div class="text-5xl text-teal-500 mb-4"><i class="fas fa-utensils"></i></div>
                    <h3 class="text-xl font-semibold text-blue-800 mb-2">Gourmet Dining</h3>
                    <p class="text-gray-600">Experience exquisite culinary delights at our world-class restaurants.</p>
                </div>

                <!-- Amenity Card 3: Spa & Wellness -->
                <div class="p-6 rounded-xl shadow-lg bg-blue-50 hover:bg-blue-100 transition-colors duration-300" data-aos="fade-up" data-aos-delay="300">
                    <div class="text-5xl text-teal-500 mb-4"><i class="fas fa-spa"></i></div>
                    <h3 class="text-xl font-semibold text-blue-800 mb-2">Spa & Wellness</h3>
                    <p class="text-gray-600">Rejuvenate your mind and body with our luxurious spa treatments.</p>
                </div>

                <!-- Amenity Card 4: Private Beach -->
                <div class="p-6 rounded-xl shadow-lg bg-blue-50 hover:bg-blue-100 transition-colors duration-300" data-aos="fade-up" data-aos-delay="400">
                    <div class="text-5xl text-teal-500 mb-4"><i class="fas fa-umbrella-beach"></i></div>
                    <h3 class="text-xl font-semibold text-blue-800 mb-2">Private Beach Access</h3>
                    <p class="text-gray-600">Enjoy exclusive access to our pristine private beach.</p>
                </div>

                <!-- Amenity Card 5: Fitness Center -->
                <div class="p-6 rounded-xl shadow-lg bg-blue-50 hover:bg-blue-100 transition-colors duration-300" data-aos="fade-up" data-aos-delay="100">
                    <div class="text-5xl text-teal-500 mb-4"><i class="fas fa-dumbbell"></i></div>
                    <h3 class="text-xl font-semibold text-blue-800 mb-2">State-of-art Fitness Center</h3>
                    <p class="text-gray-600">Stay active with our fully equipped gym and personal trainers.</p>
                </div>

                <!-- Amenity Card 6: Kids Club -->
                <div class="p-6 rounded-xl shadow-lg bg-blue-50 hover:bg-blue-100 transition-colors duration-300" data-aos="fade-up" data-aos-delay="200">
                    <div class="text-5xl text-teal-500 mb-4"><i class="fas fa-child"></i></div>
                    <h3 class="text-xl font-semibold text-blue-800 mb-2">Kids Club</h3>
                    <p class="text-gray-600">Fun and engaging activities for our younger guests.</p>
                </div>

                <!-- Amenity Card 7: Concierge Service -->
                <div class="p-6 rounded-xl shadow-lg bg-blue-50 hover:bg-blue-100 transition-colors duration-300" data-aos="fade-up" data-aos-delay="300">
                    <div class="text-5xl text-teal-500 mb-4"><i class="fas fa-concierge-bell"></i></div>
                    <h3 class="text-xl font-semibold text-blue-800 mb-2">24/7 Concierge Service</h3>
                    <p class="text-gray-600">Dedicated service to cater to your every need, anytime.</p>
                </div>

                <!-- Amenity Card 8: High-Speed Wi-Fi -->
                <div class="p-6 rounded-xl shadow-lg bg-blue-50 hover:bg-blue-100 transition-colors duration-300" data-aos="fade-up" data-aos-delay="400">
                    <div class="text-5xl text-teal-500 mb-4"><i class="fas fa-wifi"></i></div>
                    <h3 class="text-xl font-semibold text-blue-800 mb-2">High-Speed Wi-Fi</h3>
                    <p class="text-gray-600">Stay connected with complimentary high-speed internet access.</p>
                </div>

            </div>
        </div>
    </section>

    <!-- Gallery Section -->
    <section id="gallery" class="py-20 bg-gray-100">
        <div class="container mx-auto px-4">
            <h2 class="section-title text-center">Our Resort Gallery</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mt-16">

                <!-- Gallery Item 1 -->
                <div class="gallery-item relative rounded-xl overflow-hidden shadow-lg group" data-aos="fade-up" data-aos-delay="100">
                    <img src="https://images.unsplash.com/photo-1544111303-36c1e13a968b?q=80&w=2950&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" onerror="this.onerror=null;this.src='https://placehold.co/600x400/4CAF50/FFFFFF?text=Resort+Exterior';" alt="Resort Exterior" class="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110">
                    <div class="gallery-item-overlay absolute inset-0 bg-black bg-opacity-60 flex items-center justify-center text-white text-xl font-semibold">
                        <p>Resort Exterior</p>
                    </div>
                </div>

                <!-- Gallery Item 2 -->
                <div class="gallery-item relative rounded-xl overflow-hidden shadow-lg group" data-aos="fade-up" data-aos-delay="200">
                    <img src="https://images.unsplash.com/photo-1560965022-b5e02422c54d?q=80&w=2950&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" onerror="this.onerror=null;this.src='https://placehold.co/600x400/2196F3/FFFFFF?text=Lobby+View';" alt="Lobby View" class="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110">
                    <div class="gallery-item-overlay absolute inset-0 bg-black bg-opacity-60 flex items-center justify-center text-white text-xl font-semibold">
                        <p>Grand Lobby</p>
                    </div>
                </div>

                <!-- Gallery Item 3 -->
                <div class="gallery-item relative rounded-xl overflow-hidden shadow-lg group" data-aos="fade-up" data-aos-delay="300">
                    <img src="https://images.unsplash.com/photo-1563911303530-9b3780362f74?q=80&w=2950&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" onerror="this.onerror=null;this.src='https://placehold.co/600x400/FFC107/FFFFFF?text=Poolside+Relax';" alt="Poolside Relax" class="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110">
                    <div class="gallery-item-overlay absolute inset-0 bg-black bg-opacity-60 flex items-center justify-center text-white text-xl font-semibold">
                        <p>Poolside Relax</p>
                    </div>
                </div>

                <!-- Gallery Item 4 -->
                <div class="gallery-item relative rounded-xl overflow-hidden shadow-lg group" data-aos="fade-up" data-aos-delay="400">
                    <img src="https://images.unsplash.com/photo-1558212130-3161c51480f2?q=80&w=2950&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" onerror="this.onerror=null;this.src='https://placehold.co/600x400/9C27B0/FFFFFF?text=Beachfront+View';" alt="Beachfront View" class="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110">
                    <div class="gallery-item-overlay absolute inset-0 bg-black bg-opacity-60 flex items-center justify-center text-white text-xl font-semibold">
                        <p>Beachfront</p>
                    </div>
                </div>

                <!-- Gallery Item 5 -->
                <div class="gallery-item relative rounded-xl overflow-hidden shadow-lg group" data-aos="fade-up" data-aos-delay="500">
                    <img src="https://images.unsplash.com/photo-1590442385108-a53f09f06d7d?q=80&w=2950&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" onerror="this.onerror=null;this.src='https://placehold.co/600x400/E91E63/FFFFFF?text=Spa+Interior';" alt="Spa Interior" class="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110">
                    <div class="gallery-item-overlay absolute inset-0 bg-black bg-opacity-60 flex items-center justify-center text-white text-xl font-semibold">
                        <p>Serene Spa</p>
                    </div>
                </div>

                <!-- Gallery Item 6 -->
                <div class="gallery-item relative rounded-xl overflow-hidden shadow-lg group" data-aos="fade-up" data-aos-delay="600">
                    <img src="https://images.unsplash.com/photo-1517454226101-f10bf8b5a04e?q=80&w=2950&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" onerror="this.onerror=null;this.src='https://placehold.co/600x400/00BCD4/FFFFFF?text=Resort+Restaurant';" alt="Resort Restaurant" class="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110">
                    <div class="gallery-item-overlay absolute inset-0 bg-black bg-opacity-60 flex items-center justify-center text-white text-xl font-semibold">
                        <p>Fine Dining</p>
                    </div>
                </div>

            </div>
        </div>
    </section>

    <!-- Book Now Section -->
    <section id="book-now" class="py-20 bg-blue-900 text-white">
        <div class="container mx-auto px-4">
            <h2 class="section-title text-center text-white">Book Your Luxurious Stay</h2>
            <div class="max-w-3xl mx-auto bg-white p-8 md:p-12 rounded-xl shadow-2xl mt-16 text-gray-800">
                <form id="booking-form" class="space-y-6">
                    <div>
                        <label for="name" class="block text-lg font-medium text-gray-700 mb-2">Full Name</label>
                        <input type="text" id="name" name="name" required class="w-full px-4 py-3 rounded-lg border-2 border-gray-300 focus:border-teal-500 focus:ring focus:ring-teal-200 transition-all duration-200" placeholder="John Doe">
                    </div>
                    <div>
                        <label for="email" class="block text-lg font-medium text-gray-700 mb-2">Email Address</label>
                        <input type="email" id="email" name="email" required class="w-full px-4 py-3 rounded-lg border-2 border-gray-300 focus:border-teal-500 focus:ring focus:ring-teal-200 transition-all duration-200" placeholder="<EMAIL>">
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="check-in" class="block text-lg font-medium text-gray-700 mb-2">Check-in Date</label>
                            <input type="date" id="check-in" name="check-in" required class="w-full px-4 py-3 rounded-lg border-2 border-gray-300 focus:border-teal-500 focus:ring focus:ring-teal-200 transition-all duration-200">
                        </div>
                        <div>
                            <label for="check-out" class="block text-lg font-medium text-gray-700 mb-2">Check-out Date</label>
                            <input type="date" id="check-out" name="check-out" required class="w-full px-4 py-3 rounded-lg border-2 border-gray-300 focus:border-teal-500 focus:ring focus:ring-teal-200 transition-all duration-200">
                        </div>
                    </div>
                    <div>
                        <label for="room-type-select" class="block text-lg font-medium text-gray-700 mb-2">Preferred Room Type</label>
                        <select id="room-type-select" name="room-type" class="w-full px-4 py-3 rounded-lg border-2 border-gray-300 focus:border-teal-500 focus:ring focus:ring-teal-200 transition-all duration-200">
                            <option value="deluxe">Deluxe Room</option>
                            <option value="executive">Executive Suite</option>
                            <option value="presidential">Presidential Villa</option>
                        </select>
                    </div>
                    <div>
                        <label for="guests" class="block text-lg font-medium text-gray-700 mb-2">Number of Guests</label>
                        <input type="number" id="guests" name="guests" min="1" value="1" required class="w-full px-4 py-3 rounded-lg border-2 border-gray-300 focus:border-teal-500 focus:ring focus:ring-teal-200 transition-all duration-200">
                    </div>
                    <button type="submit" class="btn-primary w-full mt-6">Confirm Booking</button>
                </form>
                <div id="booking-message" class="mt-6 p-4 rounded-lg text-center bg-green-100 text-green-800 hidden">
                    <!-- Booking confirmation message will appear here -->
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-10">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2024 SaaSnext Resort Stay. All rights reserved.</p>
            <div class="flex justify-center space-x-6 mt-4 text-2xl">
                <a href="#" class="text-gray-300 hover:text-teal-400 transition-colors duration-200"><i class="fab fa-facebook-f"></i></a>
                <a href="#" class="text-gray-300 hover:text-teal-400 transition-colors duration-200"><i class="fab fa-twitter"></i></a>
                <a href="#" class="text-gray-300 hover:text-teal-400 transition-colors duration-200"><i class="fab fa-instagram"></i></a>
                <a href="#" class="text-gray-300 hover:text-teal-400 transition-colors duration-200"><i class="fab fa-linkedin-in"></i></a>
            </div>
            <div class="mt-6 text-gray-400 text-sm">
                <p>123 Resort Lane, Paradise City, PL 12345</p>
                <p>Phone: +**************** | Email: <EMAIL></p>
            </div>
        </div>
    </footer>

    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true, // Whether animation should happen only once - while scrolling down
        });

        // Room Card Slide-in Animation (Intersection Observer)
        const roomCards = document.querySelectorAll('.room-card');
        const observerOptions = {
            root: null,
            rootMargin: '0px',
            threshold: 0.1 // Trigger when 10% of the item is visible
        };

        const observer = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.remove('hidden-left');
                    entry.target.classList.add('visible');
                    // Stop observing once it's visible to avoid re-triggering
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        roomCards.forEach(card => {
            observer.observe(card);
        });

        // Room Card Image Sliders
        document.querySelectorAll('.room-slider').forEach(slider => {
            const images = slider.querySelectorAll('img');
            const dots = slider.querySelectorAll('.slider-dot');
            let currentIndex = 0;

            function showImage(index) {
                images.forEach((img, i) => {
                    img.classList.add('hidden');
                    if (i === index) {
                        img.classList.remove('hidden');
                    }
                });
                dots.forEach((dot, i) => {
                    dot.classList.remove('bg-white');
                    dot.classList.add('bg-gray-400');
                    if (i === index) {
                        dot.classList.add('bg-white');
                        dot.classList.remove('bg-gray-400');
                    }
                });
            }

            // Initial display
            showImage(currentIndex);

            // Add event listeners to dots
            dots.forEach((dot, index) => {
                dot.addEventListener('click', () => {
                    currentIndex = index;
                    showImage(currentIndex);
                });
            });

            // Auto-advance slider
            setInterval(() => {
                currentIndex = (currentIndex + 1) % images.length;
                showImage(currentIndex);
            }, 5000); // Change image every 5 seconds
        });


        // Booking Form Submission Handling
        const bookingForm = document.getElementById('booking-form');
        const bookingMessage = document.getElementById('booking-message');

        bookingForm.addEventListener('submit', function(event) {
            event.preventDefault(); // Prevent default form submission

            const formData = new FormData(bookingForm);
            const bookingDetails = {};
            for (const [key, value] of formData.entries()) {
                bookingDetails[key] = value;
            }

            console.log('Booking Details:', bookingDetails); // Log details to console

            // Simulate a successful booking
            bookingMessage.classList.remove('hidden');
            bookingMessage.classList.add('bg-green-100', 'text-green-800');
            bookingMessage.innerHTML = `<p class="font-bold">Booking Confirmed!</p><p>Thank you, ${bookingDetails.name}. We've received your request for a ${bookingDetails['room-type']} from ${bookingDetails['check-in']} to ${bookingDetails['check-out']}. A confirmation email has been sent to ${bookingDetails.email}.</p>`;

            // You would typically send this data to a server here (e.g., using fetch API)
            // fetch('/api/book', {
            //     method: 'POST',
            //     headers: {
            //         'Content-Type': 'application/json',
            //     },
            //     body: JSON.stringify(bookingDetails),
            // })
            // .then(response => response.json())
            // .then(data => {
            //     if (data.success) {
            //         bookingMessage.classList.remove('hidden');
            //         bookingMessage.classList.add('bg-green-100', 'text-green-800');
            //         bookingMessage.textContent = 'Booking confirmed! A confirmation email has been sent.';
            //         bookingForm.reset(); // Clear the form
            //     } else {
            //         bookingMessage.classList.remove('hidden');
            //         bookingMessage.classList.add('bg-red-100', 'text-red-800');
            //         bookingMessage.textContent = 'Booking failed. Please try again.';
            //     }
            // })
            // .catch(error => {
            //     console.error('Error:', error);
            //     bookingMessage.classList.remove('hidden');
            //     bookingMessage.classList.add('bg-red-100', 'text-red-800');
            //     bookingMessage.textContent = 'An error occurred during booking. Please try again later.';
            // });
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

    </script>
</body>
</html>
