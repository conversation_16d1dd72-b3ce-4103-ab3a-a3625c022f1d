<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SaaSnext Health Care</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts - Inter -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            scroll-behavior: smooth;
            overflow-x: hidden; /* Prevent horizontal scroll */
        }
        /* Custom styles for animations */
        .slide-in-card {
            opacity: 0;
            transform: translateY(50px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .slide-in-card.visible {
            opacity: 1;
            transform: translateY(0);
        }
        .stat-number {
            font-variant-numeric: tabular-nums; /* Ensures numbers align even with changing digits */
        }
        /* Modal overlay */
        .modal-overlay {
            background-color: rgba(0, 0, 0, 0.7);
        }
        /* For FAQ accordion */
        .accordion-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        .accordion-content.open {
            max-height: 200px; /* Adjust as needed for content length */
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">

    <!-- Header -->
    <header class="bg-white shadow-lg py-4 fixed top-0 left-0 right-0 z-50 rounded-b-lg">
        <div class="container mx-auto px-4 flex justify-between items-center">
            <!-- Logo -->
            <a href="#" class="text-2xl font-bold text-indigo-600">SaaSnext <span class="text-green-500">Health</span></a>

            <!-- Navigation and Emergency Number -->
            <nav class="flex items-center space-x-6">
                <ul class="hidden md:flex space-x-6">
                    <li><a href="#hero" class="text-gray-600 hover:text-indigo-600 font-medium rounded-md py-2 px-3 transition duration-300 ease-in-out">Home</a></li>
                    <li><a href="#departments" class="text-gray-600 hover:text-indigo-600 font-medium rounded-md py-2 px-3 transition duration-300 ease-in-out">Departments</a></li>
                    <li><a href="#services" class="text-gray-600 hover:text-indigo-600 font-medium rounded-md py-2 px-3 transition duration-300 ease-in-out">Services</a></li>
                    <li><a href="#team" class="text-gray-600 hover:text-indigo-600 font-medium rounded-md py-2 px-3 transition duration-300 ease-in-out">Our Team</a></li>
                    <li><a href="#testimonials" class="text-gray-600 hover:text-indigo-600 font-medium rounded-md py-2 px-3 transition duration-300 ease-in-out">Testimonials</a></li>
                    <li><a href="#faq" class="text-gray-600 hover:text-indigo-600 font-medium rounded-md py-2 px-3 transition duration-300 ease-in-out">FAQ</a></li>
                    <li><a href="#appointment" class="text-gray-600 hover:text-indigo-600 font-medium rounded-md py-2 px-3 transition duration-300 ease-in-out">Appointment</a></li>
                    <li><a href="#contact" class="text-gray-600 hover:text-indigo-600 font-medium rounded-md py-2 px-3 transition duration-300 ease-in-out">Contact</a></li>
                </ul>
                <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.984l-2.078 2.078a1 1 0 01-1.414-1.414l2.078-2.078A8.841 8.841 0 012 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9a1 1 0 000 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path></svg>
                    <span class="text-red-600 font-semibold text-lg">Emergency: ************</span>
                </div>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="hero" class="relative bg-gradient-to-r from-indigo-700 to-blue-600 py-32 mt-16 overflow-hidden rounded-bl-3xl rounded-br-3xl">
        <div class="container mx-auto px-4 flex flex-col md:flex-row items-center justify-between relative z-10">
            <!-- Text Content -->
            <div class="md:w-1/2 text-white text-center md:text-left mb-10 md:mb-0">
                <h1 class="text-5xl md:text-6xl font-extrabold leading-tight mb-6">
                    Your Health, Our Priority: <span class="text-green-300">Expert Care You Can Trust.</span>
                </h1>
                <p class="text-xl md:text-2xl mb-8 opacity-90">
                    Comprehensive healthcare solutions tailored for a healthier you and your family.
                </p>
                <a href="#appointment" class="inline-block bg-white text-indigo-700 font-bold py-3 px-8 rounded-full shadow-lg hover:bg-gray-100 transform hover:scale-105 transition duration-300 ease-in-out">
                    Book an Appointment
                </a>
            </div>
            <!-- Doctor Photo -->
            <div class="md:w-1/2 flex justify-center md:justify-end">
                <img src="https://images.unsplash.com/photo-**********-2b716b177267?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Smiling Doctor" class="rounded-full shadow-2xl border-4 border-white transform hover:scale-105 transition-transform duration-500 ease-in-out">
            </div>
        </div>
        <!-- Background elements (optional, for visual flair) -->
        <div class="absolute inset-0 z-0 flex items-center justify-center">
            <div class="w-96 h-96 bg-indigo-500 rounded-full opacity-20 animate-pulse-slow"></div>
        </div>
    </section>

    <!-- Departments Section -->
    <section id="departments" class="py-20 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-4xl font-bold text-center text-indigo-700 mb-12">Our Specialized Departments</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Department Card 1 - Cardiology -->
                <div class="bg-white rounded-lg shadow-xl p-8 text-center slide-in-card hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 ease-in-out">
                    <img src="https://placehold.co/100x100/DC2626/ffffff?text=❤️" alt="Cardiology Icon" class="mx-auto mb-6 rounded-full">
                    <h3 class="text-2xl font-semibold text-indigo-600 mb-3">Cardiology</h3>
                    <p class="text-gray-600">Expert care for your heart health, offering advanced diagnostics and treatment plans.</p>
                </div>
                <!-- Department Card 2 - Pediatrics -->
                <div class="bg-white rounded-lg shadow-xl p-8 text-center slide-in-card hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 ease-in-out">
                    <img src="https://placehold.co/100x100/22C55E/ffffff?text=👶" alt="Pediatrics Icon" class="mx-auto mb-6 rounded-full">
                    <h3 class="text-2xl font-semibold text-green-600 mb-3">Pediatrics</h3>
                    <p class="text-gray-600">Dedicated to the health and well-being of children, from infants to adolescents.</p>
                </div>
                <!-- Department Card 3 - Orthopedics -->
                <div class="bg-white rounded-lg shadow-xl p-8 text-center slide-in-card hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 ease-in-out">
                    <img src="https://placehold.co/100x100/F59E0B/ffffff?text=🦴" alt="Orthopedics Icon" class="mx-auto mb-6 rounded-full">
                    <h3 class="text-2xl font-semibold text-yellow-600 mb-3">Orthopedics</h3>
                    <p class="text-600">Comprehensive treatment for musculoskeletal conditions, ensuring mobility and comfort.</p>
                </div>
                <!-- Department Card 4 - Neurology -->
                <div class="bg-white rounded-lg shadow-xl p-8 text-center slide-in-card hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 ease-in-out">
                    <img src="https://placehold.co/100x100/3B82F6/ffffff?text=🧠" alt="Neurology Icon" class="mx-auto mb-6 rounded-full">
                    <h3 class="text-2xl font-semibold text-blue-600 mb-3">Neurology</h3>
                    <p class="text-gray-600">Specialized care for disorders of the brain, spinal cord, and nervous system.</p>
                </div>
                <!-- Department Card 5 - Dermatology -->
                <div class="bg-white rounded-lg shadow-xl p-8 text-center slide-in-card hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 ease-in-out">
                    <img src="https://placehold.co/100x100/EC4899/ffffff?text=✨" alt="Dermatology Icon" class="mx-auto mb-6 rounded-full">
                    <h3 class="text-2xl font-semibold text-red-600 mb-3">Dermatology</h3>
                    <p class="text-gray-600">Advanced solutions for skin, hair, and nail health, including cosmetic procedures.</p>
                </div>
                <!-- Department Card 6 - Oncology -->
                <div class="bg-white rounded-lg shadow-xl p-8 text-center slide-in-card hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 ease-in-out">
                    <img src="https://placehold.co/100x100/8B5CF6/ffffff?text=🎗️" alt="Oncology Icon" class="mx-auto mb-6 rounded-full">
                    <h3 class="text-2xl font-semibold text-purple-600 mb-3">Oncology</h3>
                    <p class="text-gray-600">Compassionate and cutting-edge cancer care, from diagnosis to survivorship.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Animated Stats Section -->
    <section id="stats" class="bg-indigo-600 py-16 rounded-tl-3xl rounded-br-3xl">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-white text-center">
                <div class="p-4 rounded-lg shadow-md bg-indigo-700/50">
                    <div class="text-5xl font-extrabold stat-number" data-target="25000">+0</div>
                    <p class="text-xl mt-2">Patients Served</p>
                </div>
                <div class="p-4 rounded-lg shadow-md bg-indigo-700/50">
                    <div class="text-5xl font-extrabold stat-number" data-target="15">+0</div>
                    <p class="text-xl mt-2">Years in Service</p>
                </div>
                <div class="p-4 rounded-lg shadow-md bg-indigo-700/50">
                    <div class="text-5xl font-extrabold stat-number" data-target="80">+0</div>
                    <p class="text-xl mt-2">Expert Doctors</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-20 bg-blue-50">
        <div class="container mx-auto px-4">
            <h2 class="text-4xl font-bold text-center text-indigo-700 mb-12">Our Comprehensive Services</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-white rounded-lg shadow-xl p-8 text-center slide-in-card hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 ease-in-out">
                    <img src="https://placehold.co/100x100/60A5FA/ffffff?text=💉" alt="Vaccinations Icon" class="mx-auto mb-6 rounded-full">
                    <h3 class="text-2xl font-semibold text-blue-600 mb-3">Vaccinations</h3>
                    <p class="text-gray-600">Protecting your health with a wide range of essential vaccinations for all ages.</p>
                </div>
                <div class="bg-white rounded-lg shadow-xl p-8 text-center slide-in-card hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 ease-in-out">
                    <img src="https://placehold.co/100x100/34D399/ffffff?text=🩺" alt="General Check-ups Icon" class="mx-auto mb-6 rounded-full">
                    <h3 class="text-2xl font-semibold text-green-600 mb-3">General Check-ups</h3>
                    <p class="text-gray-600">Routine health examinations to monitor your well-being and detect issues early.</p>
                </div>
                <div class="bg-white rounded-lg shadow-xl p-8 text-center slide-in-card hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 ease-in-out">
                    <img src="https://placehold.co/100x100/A78BFA/ffffff?text=🧪" alt="Diagnostic Tests Icon" class="mx-auto mb-6 rounded-full">
                    <h3 class="text-2xl font-semibold text-indigo-600 mb-3">Diagnostic Tests</h3>
                    <p class="text-gray-600">Advanced in-house laboratory and imaging services for accurate diagnoses.</p>
                </div>
                <div class="bg-white rounded-lg shadow-xl p-8 text-center slide-in-card hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 ease-in-out">
                    <img src="https://placehold.co/100x100/FACC15/ffffff?text=💊" alt="Pharmacy Services Icon" class="mx-auto mb-6 rounded-full">
                    <h3 class="text-2xl font-semibold text-yellow-600 mb-3">Pharmacy Services</h3>
                    <p class="text-gray-600">Convenient access to prescribed medications and expert pharmaceutical advice.</p>
                </div>
                <div class="bg-white rounded-lg shadow-xl p-8 text-center slide-in-card hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 ease-in-out">
                    <img src="https://placehold.co/100x100/EF4444/ffffff?text=🩹" alt="Emergency Care Icon" class="mx-auto mb-6 rounded-full">
                    <h3 class="text-2xl font-semibold text-red-600 mb-3">Minor Emergency Care</h3>
                    <p class="text-gray-600">Immediate care for non-life-threatening injuries and sudden illnesses.</p>
                </div>
                <div class="bg-white rounded-lg shadow-xl p-8 text-center slide-in-card hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 ease-in-out">
                    <img src="https://placehold.co/100x100/6366F1/ffffff?text=➕" alt="Wellness Programs Icon" class="mx-auto mb-6 rounded-full">
                    <h3 class="text-2xl font-semibold text-indigo-700 mb-3">Wellness Programs</h3>
                    <p class="text-gray-600">Programs designed to promote overall health, nutrition, and stress management.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Meet the Team Section -->
    <section id="team" class="py-20 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-4xl font-bold text-center text-indigo-700 mb-12">Meet Our Dedicated Team</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Doctor Profile 1 -->
                <div class="bg-white rounded-lg shadow-xl p-6 text-center hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 ease-in-out">
                    <img src="https://images.unsplash.com/photo-1594824458319-f538350d75a1?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Dr. Jane Smith" class="w-36 h-36 rounded-full mx-auto mb-4 border-4 border-indigo-200">
                    <h3 class="text-2xl font-semibold text-indigo-600 mb-2">Dr. Jane Smith</h3>
                    <p class="text-gray-600 mb-4">Chief Cardiologist</p>
                    <button class="bg-indigo-500 text-white py-2 px-5 rounded-full hover:bg-indigo-600 transition duration-300 ease-in-out view-profile-btn" data-doctor="smith">View Profile</button>
                </div>
                <!-- Doctor Profile 2 -->
                <div class="bg-white rounded-lg shadow-xl p-6 text-center hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 ease-in-out">
                    <img src="https://images.unsplash.com/photo-1622253692010-333f2da6031d?q=80&w=1964&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Dr. Alex Chen" class="w-36 h-36 rounded-full mx-auto mb-4 border-4 border-green-200">
                    <h3 class="text-2xl font-semibold text-green-600 mb-2">Dr. Alex Chen</h3>
                    <p class="text-gray-600 mb-4">Lead Pediatrician</p>
                    <button class="bg-indigo-500 text-white py-2 px-5 rounded-full hover:bg-indigo-600 transition duration-300 ease-in-out view-profile-btn" data-doctor="chen">View Profile</button>
                </div>
                <!-- Doctor Profile 3 -->
                <div class="bg-white rounded-lg shadow-xl p-6 text-center hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-300 ease-in-out">
                    <img src="https://images.unsplash.com/photo-162**********-430936014e7a?q=80&w=1932&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Dr. Maria Rodriguez" class="w-36 h-36 rounded-full mx-auto mb-4 border-4 border-yellow-200">
                    <h3 class="text-2xl font-semibold text-yellow-600 mb-2">Dr. Maria Rodriguez</h3>
                    <p class="text-gray-600 mb-4">Orthopedic Surgeon</p>
                    <button class="bg-indigo-500 text-white py-2 px-5 rounded-full hover:bg-indigo-600 transition duration-300 ease-in-out view-profile-btn" data-doctor="rodriguez">View Profile</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Doctor Modals -->
    <!-- Dr. Smith Modal -->
    <div id="modal-smith" class="modal-overlay fixed inset-0 flex items-center justify-center p-4 z-50 hidden">
        <div class="bg-white rounded-lg shadow-2xl p-8 max-w-lg w-full transform scale-95 opacity-0 transition-all duration-300 ease-in-out">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-3xl font-bold text-indigo-700">Dr. Jane Smith</h3>
                <button class="text-gray-500 hover:text-gray-700 text-3xl close-modal-btn">&times;</button>
            </div>
            <img src="https://images.unsplash.com/photo-1594824458319-f538350d75a1?q=80&w=1887&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Dr. Jane Smith" class="w-48 h-48 rounded-full mx-auto mb-6 border-4 border-indigo-300">
            <p class="text-lg text-gray-700 mb-4 text-center"><strong>Specialty:</strong> Chief Cardiologist</p>
            <p class="text-gray-600 leading-relaxed text-center">Dr. Jane Smith is a highly respected cardiologist with over 20 years of experience in diagnosing and treating complex heart conditions. She is passionate about preventive care and patient education, ensuring her patients lead healthy, fulfilling lives. Dr. Smith is board-certified and a fellow of the American College of Cardiology.</p>
        </div>
    </div>

    <!-- Dr. Chen Modal -->
    <div id="modal-chen" class="modal-overlay fixed inset-0 flex items-center justify-center p-4 z-50 hidden">
        <div class="bg-white rounded-lg shadow-2xl p-8 max-w-lg w-full transform scale-95 opacity-0 transition-all duration-300 ease-in-out">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-3xl font-bold text-green-700">Dr. Alex Chen</h3>
                <button class="text-gray-500 hover:text-gray-700 text-3xl close-modal-btn">&times;</button>
            </div>
            <img src="https://images.unsplash.com/photo-1622253692010-333f2da6031d?q=80&w=1964&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Dr. Alex Chen" class="w-48 h-48 rounded-full mx-auto mb-6 border-4 border-green-300">
            <p class="text-lg text-gray-700 mb-4 text-center"><strong>Specialty:</strong> Lead Pediatrician</p>
            <p class="text-gray-600 leading-relaxed text-center">Dr. Alex Chen is a dedicated pediatrician known for his warm demeanor and commitment to children's health. He specializes in developmental pediatrics and provides comprehensive care from infancy through adolescence, focusing on building strong, trusting relationships with families.</p>
        </div>
    </div>

    <!-- Dr. Rodriguez Modal -->
    <div id="modal-rodriguez" class="modal-overlay fixed inset-0 flex items-center justify-center p-4 z-50 hidden">
        <div class="bg-white rounded-lg shadow-2xl p-8 max-w-lg w-full transform scale-95 opacity-0 transition-all duration-300 ease-in-out">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-3xl font-bold text-yellow-700">Dr. Maria Rodriguez</h3>
                <button class="text-gray-500 hover:text-gray-700 text-3xl close-modal-btn">&times;</button>
            </div>
            <img src="https://images.unsplash.com/photo-162**********-430936014e7a?q=80&w=1932&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Dr. Maria Rodriguez" class="w-48 h-48 rounded-full mx-auto mb-6 border-4 border-yellow-300">
            <p class="text-lg text-gray-700 mb-4 text-center"><strong>Specialty:</strong> Orthopedic Surgeon</p>
            <p class="text-gray-600 leading-relaxed text-center">Dr. Maria Rodriguez is an accomplished orthopedic surgeon specializing in joint replacement and sports medicine. Her patient-centered approach ensures personalized treatment plans, helping patients regain mobility and improve their quality of life. She is renowned for her surgical precision and excellent outcomes.</p>
        </div>
    </div>

    <!-- Testimonials Section -->
    <section id="testimonials" class="py-20 bg-gradient-to-r from-blue-600 to-indigo-700 text-white rounded-tl-3xl rounded-br-3xl">
        <div class="container mx-auto px-4">
            <h2 class="text-4xl font-bold text-center mb-12">What Our Patients Say</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Testimonial 1 -->
                <div class="bg-white text-gray-800 p-8 rounded-lg shadow-xl slide-in-card">
                    <p class="italic text-lg mb-4">"SaaSnext Health Care provided exceptional service. The doctors were attentive, and the facilities were top-notch. I highly recommend them!"</p>
                    <p class="font-semibold text-indigo-600">- Sarah L.</p>
                    <p class="text-gray-500 text-sm">Patient since 2022</p>
                </div>
                <!-- Testimonial 2 -->
                <div class="bg-white text-gray-800 p-8 rounded-lg shadow-xl slide-in-card">
                    <p class="italic text-lg mb-4">"The pediatric department is amazing! My kids felt so comfortable, and Dr. Chen was incredibly thorough and caring."</p>
                    <p class="font-semibold text-green-600">- Mark R.</p>
                    <p class="text-gray-500 text-sm">Parent of 2</p>
                </div>
                <!-- Testimonial 3 -->
                <div class="bg-white text-gray-800 p-8 rounded-lg shadow-xl slide-in-card">
                    <p class="italic text-lg mb-4">"From appointment booking to recovery, everything was smooth. Dr. Rodriguez performed my surgery flawlessly. Truly grateful!"</p>
                    <p class="font-semibold text-yellow-600">- David P.</p>
                    <p class="text-gray-500 text-sm">Recovered patient</p>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section id="faq" class="py-20 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-4xl font-bold text-center text-indigo-700 mb-12">Frequently Asked Questions</h2>
            <div class="max-w-3xl mx-auto">
                <!-- FAQ Item 1 -->
                <div class="mb-4 bg-white rounded-lg shadow-md">
                    <button class="accordion-header w-full flex justify-between items-center p-6 text-left font-semibold text-xl text-indigo-600 focus:outline-none">
                        <span>What are your clinic hours?</span>
                        <svg class="w-6 h-6 transform transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>
                    </button>
                    <div class="accordion-content px-6 pb-4 text-gray-700">
                        <p>Our clinic is open Monday to Friday from 8:00 AM to 6:00 PM, and Saturday from 9:00 AM to 1:00 PM. We are closed on Sundays and public holidays.</p>
                    </div>
                </div>
                <!-- FAQ Item 2 -->
                <div class="mb-4 bg-white rounded-lg shadow-md">
                    <button class="accordion-header w-full flex justify-between items-center p-6 text-left font-semibold text-xl text-indigo-600 focus:outline-none">
                        <span>Do you accept my insurance?</span>
                        <svg class="w-6 h-6 transform transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>
                    </button>
                    <div class="accordion-content px-6 pb-4 text-gray-700">
                        <p>We accept most major insurance plans. Please contact our billing department or check our website's 'Insurance' section for a detailed list. It's always best to verify your coverage directly with your insurance provider prior to your visit.</p>
                    </div>
                </div>
                <!-- FAQ Item 3 -->
                <div class="mb-4 bg-white rounded-lg shadow-md">
                    <button class="accordion-header w-full flex justify-between items-center p-6 text-left font-semibold text-xl text-indigo-600 focus:outline-none">
                        <span>How can I request my medical records?</span>
                        <svg class="w-6 h-6 transform transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>
                    </button>
                    <div class="accordion-content px-6 pb-4 text-gray-700">
                        <p>You can request your medical records by filling out a release form available at our front desk or by downloading it from our website. Please allow 5-7 business days for processing.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Book Appointment Section -->
    <section id="appointment" class="py-20 bg-blue-50">
        <div class="container mx-auto px-4">
            <h2 class="text-4xl font-bold text-center text-indigo-700 mb-12">Book Your Appointment</h2>
            <div class="max-w-3xl mx-auto bg-white rounded-lg shadow-xl p-8">
                <form id="appointmentForm" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-gray-700 text-lg font-medium mb-2">Full Name</label>
                        <input type="text" id="name" name="name" class="w-full px-4 py-3 rounded-lg border-2 border-gray-300 focus:outline-none focus:border-indigo-500 transition duration-200" placeholder="Your Name" required>
                        <p class="text-red-500 text-sm mt-1 hidden" id="nameError">Name is required.</p>
                    </div>
                    <div>
                        <label for="email" class="block text-gray-700 text-lg font-medium mb-2">Email Address</label>
                        <input type="email" id="email" name="email" class="w-full px-4 py-3 rounded-lg border-2 border-gray-300 focus:outline-none focus:border-indigo-500 transition duration-200" placeholder="<EMAIL>" required>
                        <p class="text-red-500 text-sm mt-1 hidden" id="emailError">Please enter a valid email address.</p>
                    </div>
                    <div>
                        <label for="phone" class="block text-gray-700 text-lg font-medium mb-2">Phone Number</label>
                        <input type="tel" id="phone" name="phone" class="w-full px-4 py-3 rounded-lg border-2 border-gray-300 focus:outline-none focus:border-indigo-500 transition duration-200" placeholder="e.g., ************" pattern="[0-9]{3}-[0-9]{3}-[0-9]{4}" title="Format: ************" required>
                        <p class="text-red-500 text-sm mt-1 hidden" id="phoneError">Please enter a valid phone number (e.g., ************).</p>
                    </div>
                    <div>
                        <label for="date" class="block text-gray-700 text-lg font-medium mb-2">Preferred Date</label>
                        <input type="date" id="date" name="date" class="w-full px-4 py-3 rounded-lg border-2 border-gray-300 focus:outline-none focus:border-indigo-500 transition duration-200" required>
                        <p class="text-red-500 text-sm mt-1 hidden" id="dateError">Please select a preferred date.</p>
                    </div>
                    <div class="md:col-span-2">
                        <label for="department" class="block text-gray-700 text-lg font-medium mb-2">Select Department</label>
                        <select id="department" name="department" class="w-full px-4 py-3 rounded-lg border-2 border-gray-300 focus:outline-none focus:border-indigo-500 transition duration-200" required>
                            <option value="">-- Select a Department --</option>
                            <option value="Cardiology">Cardiology</option>
                            <option value="Pediatrics">Pediatrics</option>
                            <option value="Orthopedics">Orthopedics</option>
                            <option value="Neurology">Neurology</option>
                            <option value="Dermatology">Dermatology</option>
                            <option value="Oncology">Oncology</option>
                            <option value="General Practice">General Practice</option>
                        </select>
                        <p class="text-red-500 text-sm mt-1 hidden" id="departmentError">Please select a department.</p>
                    </div>
                    <div class="md:col-span-2">
                        <label for="message" class="block text-gray-700 text-lg font-medium mb-2">Your Message (Optional)</label>
                        <textarea id="message" name="message" rows="4" class="w-full px-4 py-3 rounded-lg border-2 border-gray-300 focus:outline-none focus:border-indigo-500 transition duration-200" placeholder="Any specific symptoms or requests?"></textarea>
                    </div>
                    <div class="md:col-span-2 text-center">
                        <button type="submit" class="bg-indigo-600 text-white font-bold py-3 px-8 rounded-full shadow-lg hover:bg-indigo-700 transform hover:scale-105 transition duration-300 ease-in-out">
                            Submit Appointment
                        </button>
                    </div>
                    <div id="formMessage" class="md:col-span-2 text-center mt-4 text-lg font-semibold hidden"></div>
                </form>
            </div>
        </div>
    </section>

    <!-- Contact Us Section -->
    <section id="contact" class="py-20 bg-indigo-500 text-white rounded-tl-3xl rounded-br-3xl">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-4xl font-bold mb-12">Contact Us</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <h3 class="text-2xl font-semibold mb-4">Our Location</h3>
                    <p class="text-lg">123 Health Street, Wellness City, HW 54321</p>
                    <p class="text-lg mt-2">
                        <a href="https://maps.app.goo.gl/YourClinicLocation" target="_blank" class="text-blue-200 hover:underline">View on Map</a>
                    </p>
                </div>
                <div>
                    <h3 class="text-2xl font-semibold mb-4">Contact Details</h3>
                    <p class="text-lg">Phone: <a href="tel:**********" class="text-blue-200 hover:underline">************</a></p>
                    <p class="text-lg">Email: <a href="mailto:<EMAIL>" class="text-blue-200 hover:underline"><EMAIL></a></p>
                </div>
                <div>
                    <h3 class="text-2xl font-semibold mb-4">Clinic Hours</h3>
                    <p class="text-lg">Mon - Fri: 8:00 AM - 6:00 PM</p>
                    <p class="text-lg">Saturday: 9:00 AM - 1:00 PM</p>
                    <p class="text-lg">Sunday: Closed</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-10 rounded-t-lg">
        <div class="container mx-auto px-4 text-center">
            <p class="mb-4">&copy; 2024 SaaSnext Health Care. All rights reserved.</p>
            <div class="flex justify-center space-x-6 text-xl">
                <a href="#" class="hover:text-indigo-400 transition-colors duration-300">Privacy Policy</a>
                <a href="#" class="hover:text-indigo-400 transition-colors duration-300">Terms of Service</a>
                <a href="#" class="hover:text-indigo-400 transition-colors duration-300">Contact Us</a>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Smooth scrolling for navigation links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });

            // Intersection Observer for slide-in cards
            const slideInCards = document.querySelectorAll('.slide-in-card');
            const slideInObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                        observer.unobserve(entry.target); // Stop observing once visible
                    }
                });
            }, { threshold: 0.1 }); // Adjust threshold as needed

            slideInCards.forEach(card => {
                slideInObserver.observe(card);
            });

            // Animated Stats Counter
            const statNumbers = document.querySelectorAll('.stat-number');
            const animateNumber = (obj, start, end, duration) => {
                let startTimestamp = null;
                const step = (timestamp) => {
                    if (!startTimestamp) startTimestamp = timestamp;
                    const progress = Math.min((timestamp - startTimestamp) / duration, 1);
                    obj.textContent = '+' + Math.floor(progress * (end - start) + start).toLocaleString();
                    if (progress < 1) {
                        window.requestAnimationFrame(step);
                    }
                };
                window.requestAnimationFrame(step);
            };

            const statsObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        statNumbers.forEach(stat => {
                            const target = parseInt(stat.dataset.target);
                            animateNumber(stat, 0, target, 2000); // Animate from 0 to target over 2 seconds
                        });
                        observer.unobserve(entry.target); // Stop observing once animated
                    }
                });
            }, { threshold: 0.5 }); // Trigger when 50% of the section is visible

            const statsSection = document.getElementById('stats');
            if (statsSection) {
                statsObserver.observe(statsSection);
            }

            // Doctor Profile Modals
            const viewProfileButtons = document.querySelectorAll('.view-profile-btn');
            const closeModals = document.querySelectorAll('.close-modal-btn');
            const modalOverlays = document.querySelectorAll('.modal-overlay');

            viewProfileButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const doctorId = button.dataset.doctor;
                    const modal = document.getElementById(`modal-${doctorId}`);
                    if (modal) {
                        modal.classList.remove('hidden');
                        setTimeout(() => {
                            modal.querySelector('div').classList.remove('scale-95', 'opacity-0');
                            modal.querySelector('div').classList.add('scale-100', 'opacity-100');
                        }, 10); // Small delay for transition to apply
                    }
                });
            });

            closeModals.forEach(button => {
                button.addEventListener('click', (event) => {
                    const modal = event.target.closest('.modal-overlay');
                    if (modal) {
                        modal.querySelector('div').classList.remove('scale-100', 'opacity-100');
                        modal.querySelector('div').classList.add('scale-95', 'opacity-0');
                        setTimeout(() => {
                            modal.classList.add('hidden');
                        }, 300); // Wait for transition to finish before hiding
                    }
                });
            });

            // Close modal when clicking outside of the modal content
            modalOverlays.forEach(overlay => {
                overlay.addEventListener('click', (event) => {
                    if (event.target === overlay) { // Check if the click was directly on the overlay
                        overlay.querySelector('div').classList.remove('scale-100', 'opacity-100');
                        overlay.querySelector('div').classList.add('scale-95', 'opacity-0');
                        setTimeout(() => {
                            overlay.classList.add('hidden');
                        }, 300);
                    }
                });
            });

            // FAQ Accordion
            const accordionHeaders = document.querySelectorAll('.accordion-header');
            accordionHeaders.forEach(header => {
                header.addEventListener('click', () => {
                    const content = header.nextElementSibling;
                    const icon = header.querySelector('svg');

                    // Close all other open accordions
                    accordionHeaders.forEach(otherHeader => {
                        if (otherHeader !== header) {
                            const otherContent = otherHeader.nextElementSibling;
                            const otherIcon = otherHeader.querySelector('svg');
                            if (otherContent.classList.contains('open')) {
                                otherContent.classList.remove('open');
                                otherContent.style.maxHeight = null;
                                otherIcon.classList.remove('rotate-180');
                            }
                        }
                    });

                    // Toggle current accordion
                    if (content.classList.contains('open')) {
                        content.classList.remove('open');
                        content.style.maxHeight = null;
                        icon.classList.remove('rotate-180');
                    } else {
                        content.classList.add('open');
                        content.style.maxHeight = content.scrollHeight + 'px'; // Set max-height to scrollHeight for smooth transition
                        icon.classList.add('rotate-180');
                    }
                });
            });

            // Appointment Form Validation
            const appointmentForm = document.getElementById('appointmentForm');
            const formMessage = document.getElementById('formMessage');

            const validateField = (inputElement, errorElement, errorMessage) => {
                if (!inputElement.value.trim()) {
                    errorElement.textContent = errorMessage;
                    errorElement.classList.remove('hidden');
                    inputElement.classList.add('border-red-500');
                    return false;
                } else {
                    errorElement.classList.add('hidden');
                    inputElement.classList.remove('border-red-500');
                    return true;
                }
            };

            const validateEmail = (inputElement, errorElement) => {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(inputElement.value.trim())) {
                    errorElement.textContent = 'Please enter a valid email address.';
                    errorElement.classList.remove('hidden');
                    inputElement.classList.add('border-red-500');
                    return false;
                } else {
                    errorElement.classList.add('hidden');
                    inputElement.classList.remove('border-red-500');
                    return true;
                }
            };

            const validatePhone = (inputElement, errorElement) => {
                // Allows for ************ or ********** or (************* etc.
                const phoneRegex = /^(\+\d{1,2}\s?)?(\(\d{3}\)|\d{3})[\s.-]?\d{3}[\s.-]?\d{4}$/;
                if (!phoneRegex.test(inputElement.value.trim())) {
                    errorElement.textContent = 'Please enter a valid phone number (e.g., ************).';
                    errorElement.classList.remove('hidden');
                    inputElement.classList.add('border-red-500');
                    return false;
                } else {
                    errorElement.classList.add('hidden');
                    inputElement.classList.remove('border-red-500');
                    return true;
                }
            };

            appointmentForm.addEventListener('submit', (e) => {
                e.preventDefault(); // Prevent default form submission

                let isValid = true;

                // Validate Name
                isValid = validateField(document.getElementById('name'), document.getElementById('nameError'), 'Name is required.') && isValid;
                // Validate Email
                isValid = validateEmail(document.getElementById('email'), document.getElementById('emailError')) && isValid;
                // Validate Phone
                isValid = validatePhone(document.getElementById('phone'), document.getElementById('phoneError')) && isValid;
                // Validate Date
                isValid = validateField(document.getElementById('date'), document.getElementById('dateError'), 'Please select a preferred date.') && isValid;
                // Validate Department
                isValid = validateField(document.getElementById('department'), document.getElementById('departmentError'), 'Please select a department.') && isValid;

                if (isValid) {
                    // Simulate form submission
                    formMessage.classList.remove('hidden', 'text-red-500');
                    formMessage.classList.add('text-green-600');
                    formMessage.textContent = 'Appointment booked successfully! We will contact you shortly.';
                    appointmentForm.reset(); // Clear the form
                    // Hide message after a few seconds
                    setTimeout(() => {
                        formMessage.classList.add('hidden');
                    }, 5000);
                } else {
                    formMessage.classList.remove('hidden', 'text-green-600');
                    formMessage.classList.add('text-red-500');
                    formMessage.textContent = 'Please correct the errors in the form.';
                }
            });

            // Add real-time validation feedback on input blur
            document.getElementById('name').addEventListener('blur', (e) => validateField(e.target, document.getElementById('nameError'), 'Name is required.'));
            document.getElementById('email').addEventListener('blur', (e) => validateEmail(e.target, document.getElementById('emailError')));
            document.getElementById('phone').addEventListener('blur', (e) => validatePhone(e.target, document.getElementById('phoneError')));
            document.getElementById('date').addEventListener('blur', (e) => validateField(e.target, document.getElementById('dateError'), 'Please select a preferred date.'));
            document.getElementById('department').addEventListener('blur', (e) => validateField(e.target, document.getElementById('departmentError'), 'Please select a department.'));
        });
    </script>
</body>
</html>
