<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Creative Tiles & Interiors</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Google Fonts - Inter -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- AOS CSS -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <style>
        /* Custom styles for Tailwind configuration */
        :root {
            --primary-color: #3B82F6; /* blue-500 */
            --secondary-color: #60A5FA; /* blue-400 */
            --dark-background: #1F2937; /* gray-800 */
            --light-text: #F9FAFB; /* gray-50 */
            --dark-text: #111827; /* gray-900 */
        }
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--light-text);
            color: var(--dark-text);
            scroll-behavior: smooth;
        }

        /* Hero Section Parallax Effect */
        .hero-parallax {
            background-image: url('https://images.pexels.com/photos/6436752/pexels-photo-6436752.jpeg'); /* Updated background image */
            background-size: cover;
            background-position: center;
            background-attachment: fixed; /* This creates the parallax effect */
        }

        /* Lightbox styles */
        .lightbox-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        .lightbox-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        .lightbox-content {
            max-width: 90%;
            max-height: 90%;
            border-radius: 0.75rem; /* rounded-xl */
            overflow: hidden;
            position: relative;
        }
        .lightbox-content img {
            width: auto;
            height: auto;
            max-width: 100%;
            max-height: 100%;
            display: block;
            margin: auto;
            border-radius: 0.75rem; /* rounded-xl */
        }
        .lightbox-close {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            border-radius: 9999px; /* rounded-full */
            width: 2.5rem;
            height: 2.5rem;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.5rem;
            cursor: pointer;
            transition: background 0.2s ease;
        }
        .lightbox-close:hover {
            background: rgba(255, 255, 255, 0.4);
        }

        /* Custom utility classes for animations (if needed beyond AOS) */
        .animate-counter {
            transition: all 1s ease-out;
        }
    </style>
    <!-- Tailwind CSS configuration for custom font and colors -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        inter: ['Inter', 'sans-serif'],
                    },
                    colors: {
                        primary: 'var(--primary-color)',
                        secondary: 'var(--secondary-color)',
                        darkBg: 'var(--dark-background)',
                        lightText: 'var(--light-text)',
                        darkText: 'var(--dark-text)',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-inter bg-lightText text-darkText antialiased">

    <!-- Hero Section -->
    <header id="home" class="hero-parallax relative h-screen flex items-center justify-center text-center text-white p-4">
        <div class="absolute inset-0 bg-black opacity-60"></div>
        <div class="relative z-10" data-aos="fade-up" data-aos-duration="1000">
            <h1 class="text-5xl md:text-7xl font-extrabold mb-4 drop-shadow-lg leading-tight">
                Creative Tiles & Interiors
            </h1>
            <p class="text-lg md:text-2xl mb-8 max-w-2xl mx-auto drop-shadow">
                Crafting exceptional spaces with premium tiles and bespoke interior solutions.
            </p>
            <a href="#contact" class="inline-block bg-primary hover:bg-secondary text-white font-bold py-3 px-8 rounded-full shadow-lg transition duration-300 ease-in-out transform hover:scale-105">
                Get a Free Consultation
            </a>
        </div>
    </header>

    <!-- Navigation Bar (Fixed on scroll) -->
    <nav id="navbar" class="fixed top-0 left-0 w-full bg-white bg-opacity-95 shadow-lg z-50 transition-all duration-300 ease-in-out hidden">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <a href="#home" class="text-xl font-bold text-darkText hover:text-primary transition-colors">Creative</a>
            <div class="flex space-x-6 md:space-x-8">
                <a href="#products" class="text-darkText hover:text-primary transition-colors font-medium">Products</a>
                <a href="#gallery" class="text-darkText hover:text-primary transition-colors font-medium">Gallery</a>
                <a href="#why-choose-us" class="text-darkText hover:text-primary transition-colors font-medium">Why Us</a>
                <a href="#contact" class="text-darkText hover:text-primary transition-colors font-medium">Contact</a>
            </div>
        </div>
    </nav>

    <main>
        <!-- Product Categories Section -->
        <section id="products" class="py-16 md:py-24 bg-gray-50">
            <div class="container mx-auto px-4">
                <h2 class="text-4xl font-bold text-center mb-12 text-darkText" data-aos="fade-up">
                    Explore Our Premium Collections
                </h2>
                <div class="grid md:grid-cols-3 gap-8">
                    <div class="bg-white rounded-xl shadow-lg p-6 md:p-8 text-center border border-gray-200 transition-all duration-300 ease-in-out hover:shadow-xl hover:scale-105 cursor-pointer"
                         data-aos="fade-up" data-aos-delay="100" onclick="document.getElementById('gallery').scrollIntoView({ behavior: 'smooth' });">
                        <img src="https://images.pexels.com/photos/1769399/pexels-photo-1769399.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="Marble Tiles" class="mx-auto mb-4 w-32 h-32 object-cover rounded-full shadow-md">
                        <h3 class="text-2xl font-semibold mb-2 text-darkText">Marble Tiles</h3>
                        <p class="text-gray-600">Timeless elegance and natural beauty for sophisticated spaces.</p>
                    </div>
                    <div class="bg-white rounded-xl shadow-lg p-6 md:p-8 text-center border border-gray-200 transition-all duration-300 ease-in-out hover:shadow-xl hover:scale-105 cursor-pointer"
                         data-aos="fade-up" data-aos-delay="200" onclick="document.getElementById('gallery').scrollIntoView({ behavior: 'smooth' });">
                        <img src="https://images.pexels.com/photos/3929424/pexels-photo-3929424.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="Vitrified Tiles" class="mx-auto mb-4 w-32 h-32 object-cover rounded-full shadow-md">
                        <h3 class="text-2xl font-semibold mb-2 text-darkText">Vitrified Tiles</h3>
                        <p class="text-gray-600">Durable, low-maintenance, and versatile for modern living.</p>
                    </div>
                    <div class="bg-white rounded-xl shadow-lg p-6 md:p-8 text-center border border-gray-200 transition-all duration-300 ease-in-out hover:shadow-xl hover:scale-105 cursor-pointer"
                         data-aos="fade-up" data-aos-delay="300" onclick="document.getElementById('gallery').scrollIntoView({ behavior: 'smooth' });">
                        <img src="https://images.pexels.com/photos/1571470/pexels-photo-1571470.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="Wall Tiles" class="mx-auto mb-4 w-32 h-32 object-cover rounded-full shadow-md">
                        <h3 class="text-2xl font-semibold mb-2 text-darkText">Wall Tiles</h3>
                        <p class="text-gray-600">Elevate your walls with exquisite designs and textures.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Gallery Section -->
        <section id="gallery" class="py-16 md:py-24 bg-white">
            <div class="container mx-auto px-4">
                <h2 class="text-4xl font-bold text-center mb-12 text-darkText" data-aos="fade-up">
                    Our Latest Projects & Inspirations
                </h2>
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                    <!-- Gallery Item 1 -->
                    <div class="group rounded-xl shadow-md overflow-hidden border border-gray-200 transform hover:scale-105 transition-transform duration-300 ease-in-out cursor-pointer"
                         data-aos="zoom-in" data-aos-delay="100">
                        <img src="https://images.pexels.com/photos/7172782/pexels-photo-7172782.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="Elegant Bathroom"
                             class="w-full h-48 object-cover rounded-t-xl group-hover:opacity-80 transition-opacity gallery-img"
                             data-large-src="https://images.pexels.com/photos/7172782/pexels-photo-7172782.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2">
                        <div class="p-4 bg-gray-50 rounded-b-xl">
                            <h3 class="text-lg font-semibold text-darkText">Elegant Bathroom</h3>
                            <p class="text-sm text-gray-600">Marble Collection</p>
                        </div>
                    </div>
                    <!-- Gallery Item 2 -->
                    <div class="group rounded-xl shadow-md overflow-hidden border border-gray-200 transform hover:scale-105 transition-transform duration-300 ease-in-out cursor-pointer"
                         data-aos="zoom-in" data-aos-delay="200">
                        <img src="https://images.pexels.com/photos/4106560/pexels-photo-4106560.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="Modern Kitchen"
                             class="w-full h-48 object-cover rounded-t-xl group-hover:opacity-80 transition-opacity gallery-img"
                             data-large-src="https://images.pexels.com/photos/4106560/pexels-photo-4106560.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2">
                        <div class="p-4 bg-gray-50 rounded-b-xl">
                            <h3 class="text-lg font-semibold text-darkText">Modern Kitchen</h3>
                            <p class="text-sm text-gray-600">Vitrified Collection</p>
                        </div>
                    </div>
                    <!-- Gallery Item 3 -->
                    <div class="group rounded-xl shadow-md overflow-hidden border border-gray-200 transform hover:scale-105 transition-transform duration-300 ease-in-out cursor-pointer"
                         data-aos="zoom-in" data-aos-delay="300">
                        <img src="https://images.pexels.com/photos/7061730/pexels-photo-7061730.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="Luxury Living Room"
                             class="w-full h-48 object-cover rounded-t-xl group-hover:opacity-80 transition-opacity gallery-img"
                             data-large-src="https://images.pexels.com/photos/7061730/pexels-photo-7061730.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2">
                        <div class="p-4 bg-gray-50 rounded-b-xl">
                            <h3 class="text-lg font-semibold text-darkText">Luxury Living Room</h3>
                            <p class="text-sm text-gray-600">Wall & Floor Tiles</p>
                        </div>
                    </div>
                    <!-- Gallery Item 4 -->
                    <div class="group rounded-xl shadow-md overflow-hidden border border-gray-200 transform hover:scale-105 transition-transform duration-300 ease-in-out cursor-pointer"
                         data-aos="zoom-in" data-aos-delay="400">
                        <img src="https://images.pexels.com/photos/417273/pexels-photo-417273.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="Outdoor Patio"
                             class="w-full h-48 object-cover rounded-t-xl group-hover:opacity-80 transition-opacity gallery-img"
                             data-large-src="https://images.pexels.com/photos/417273/pexels-photo-417273.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2">
                        <div class="p-4 bg-gray-50 rounded-b-xl">
                            <h3 class="text-lg font-semibold text-darkText">Outdoor Patio</h3>
                            <p class="text-sm text-gray-600">Durable Outdoor Tiles</p>
                        </div>
                    </div>
                    <!-- Gallery Item 5 -->
                    <div class="group rounded-xl shadow-md overflow-hidden border border-gray-200 transform hover:scale-105 transition-transform duration-300 ease-in-out cursor-pointer"
                         data-aos="zoom-in" data-aos-delay="500">
                        <img src="https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="Commercial Office"
                             class="w-full h-48 object-cover rounded-t-xl group-hover:opacity-80 transition-opacity gallery-img"
                             data-large-src="https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2">
                        <div class="p-4 bg-gray-50 rounded-b-xl">
                            <h3 class="text-lg font-semibold text-darkText">Commercial Office</h3>
                            <p class="text-sm text-gray-600">Custom Flooring</p>
                        </div>
                    </div>
                    <!-- Gallery Item 6 -->
                    <div class="group rounded-xl shadow-md overflow-hidden border border-gray-200 transform hover:scale-105 transition-transform duration-300 ease-in-out cursor-pointer"
                         data-aos="zoom-in" data-aos-delay="600">
                        <img src="https://images.pexels.com/photos/5691566/pexels-photo-5691566.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="Hotel Lobby"
                             class="w-full h-48 object-cover rounded-t-xl group-hover:opacity-80 transition-opacity gallery-img"
                             data-large-src="https://images.pexels.com/photos/5691566/pexels-photo-5691566.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2">
                        <div class="p-4 bg-gray-50 rounded-b-xl">
                            <h3 class="text-lg font-semibold text-darkText">Hotel Lobby</h3>
                            <p class="text-sm text-gray-600">Grand Interior Design</p>
                        </div>
                    </div>
                    <!-- Gallery Item 7 (The previously added image) -->
                    <div class="group rounded-xl shadow-md overflow-hidden border border-gray-200 transform hover:scale-105 transition-transform duration-300 ease-in-out cursor-pointer"
                         data-aos="zoom-in" data-aos-delay="700">
                        <img src="https://images.pexels.com/photos/7227645/pexels-photo-7227645.jpeg" alt="Contemporary Space"
                             class="w-full h-48 object-cover rounded-t-xl group-hover:opacity-80 transition-opacity gallery-img"
                             data-large-src="https://images.pexels.com/photos/7227645/pexels-photo-7227645.jpeg">
                        <div class="p-4 bg-gray-50 rounded-b-xl">
                            <h3 class="text-lg font-semibold text-darkText">Contemporary Space</h3>
                            <p class="text-sm text-gray-600">Innovative Tile Solutions</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Lightbox HTML -->
            <div id="lightbox" class="lightbox-overlay">
                <button class="lightbox-close" aria-label="Close Lightbox">&times;</button>
                <div class="lightbox-content">
                    <img id="lightbox-img" src="" alt="Large Image">
                </div>
            </div>
        </section>

        <!-- Why Choose Us Section -->
        <section id="why-choose-us" class="py-16 md:py-24 bg-gray-50">
            <div class="container mx-auto px-4">
                <h2 class="text-4xl font-bold text-center mb-12 text-darkText" data-aos="fade-up">
                    Why Choose Creative Tiles & Interiors?
                </h2>
                <div class="grid md:grid-cols-3 gap-8 text-center">
                    <div class="bg-white rounded-xl shadow-lg p-6 md:p-8 border border-gray-200" data-aos="fade-up" data-aos-delay="100">
                        <div class="text-primary text-6xl font-bold mb-4">
                            <span id="clients-count" class="counter">0</span>+
                        </div>
                        <h3 class="text-2xl font-semibold text-darkText mb-2">Happy Clients</h3>
                        <p class="text-gray-600">Our commitment to excellence ensures client satisfaction.</p>
                    </div>
                    <div class="bg-white rounded-xl shadow-lg p-6 md:p-8 border border-gray-200" data-aos="fade-up" data-aos-delay="200">
                        <div class="text-primary text-6xl font-bold mb-4">
                            <span id="projects-count" class="counter">0</span>+
                        </div>
                        <h3 class="text-2xl font-semibold text-darkText mb-2">Projects Completed</h3>
                        <p class="text-gray-600">Delivering stunning results on every project, big or small.</p>
                    </div>
                    <div class="bg-white rounded-xl shadow-lg p-6 md:p-8 border border-gray-200" data-aos="fade-up" data-aos-delay="300">
                        <div class="text-primary text-6xl font-bold mb-4">
                            <span id="sqft-count" class="counter">0</span>K+
                        </div>
                        <h3 class="text-2xl font-semibold text-darkText mb-2">Sq. Ft. Covered</h3>
                        <p class="text-gray-600">Vast experience in transforming diverse residential and commercial spaces.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Form Section -->
        <section id="contact" class="py-16 md:py-24 bg-white">
            <div class="container mx-auto px-4 max-w-2xl">
                <h2 class="text-4xl font-bold text-center mb-12 text-darkText" data-aos="fade-up">
                    Get In Touch
                </h2>
                <form id="contact-form" class="bg-gray-50 rounded-xl shadow-lg p-6 md:p-10 border border-gray-200" data-aos="fade-up" data-aos-delay="100">
                    <div class="mb-6">
                        <label for="name" class="block text-gray-700 text-sm font-bold mb-2">Name</label>
                        <input type="text" id="name" name="name" class="shadow appearance-none border border-gray-300 rounded-lg w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200" placeholder="Your Name" required>
                    </div>
                    <div class="mb-6">
                        <label for="email" class="block text-gray-700 text-sm font-bold mb-2">Email</label>
                        <input type="email" id="email" name="email" class="shadow appearance-none border border-gray-300 rounded-lg w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200" placeholder="<EMAIL>" required>
                    </div>
                    <div class="mb-6">
                        <label for="subject" class="block text-gray-700 text-sm font-bold mb-2">Subject</label>
                        <input type="text" id="subject" name="subject" class="shadow appearance-none border border-gray-300 rounded-lg w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200" placeholder="Inquiry about..." required>
                    </div>
                    <div class="mb-8">
                        <label for="message" class="block text-gray-700 text-sm font-bold mb-2">Message</label>
                        <textarea id="message" name="message" rows="6" class="shadow appearance-none border border-gray-300 rounded-lg w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200" placeholder="Tell us about your project or inquiry..." required></textarea>
                    </div>
                    <div class="flex items-center justify-center">
                        <button type="submit" class="bg-primary hover:bg-secondary text-white font-bold py-3 px-8 rounded-full shadow-lg transition duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-75">
                            Send Message
                        </button>
                    </div>
                </form>
                <div id="form-message" class="mt-4 p-3 rounded-lg text-center font-semibold hidden"></div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-darkBg text-white py-8">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2025 Creative Tiles & Interiors. All rights reserved.</p>
            <div class="mt-4">
                <a href="#" class="text-gray-400 hover:text-white mx-2">Privacy Policy</a>
                <span class="text-gray-600">|</span>
                <a href="#" class="text-gray-400 hover:text-white mx-2">Terms of Service</a>
            </div>
        </div>
    </footer>

    <!-- AOS JS -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true, // Whether animation should happen only once - while scrolling down
        });

        // Navbar visibility on scroll
        const navbar = document.getElementById('navbar');
        window.addEventListener('scroll', () => {
            if (window.scrollY > 100) { // Adjust this value to when you want the navbar to appear
                navbar.classList.remove('hidden');
                navbar.classList.add('animate-slide-down'); /* Optional animation class */
            } else {
                navbar.classList.add('hidden');
                navbar.classList.remove('animate-slide-down');
            }
        });

        // Animated Counters
        const animateCounter = (id, target, duration, suffix = '') => {
            const element = document.getElementById(id);
            if (!element) return;

            let start = 0;
            const increment = target / (duration / 10); // Calculate increment based on 10ms interval
            const interval = setInterval(() => {
                start += increment;
                if (start >= target) {
                    element.innerText = Math.floor(target) + suffix;
                    clearInterval(interval);
                } else {
                    element.innerText = Math.floor(start) + suffix;
                }
            }, 10);
        };

        // Intersection Observer to trigger counters when section is in view
        const whyChooseUsSection = document.getElementById('why-choose-us');
        let countersAnimated = false; // Flag to ensure animation runs only once

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && !countersAnimated) {
                    animateCounter('clients-count', 250, 2000); // 250 clients over 2 seconds
                    animateCounter('projects-count', 400, 2200); // 400 projects over 2.2 seconds
                    animateCounter('sqft-count', 500, 2500); // 500K sqft over 2.5 seconds
                    countersAnimated = true; // Set flag to true after animating
                    observer.unobserve(whyChooseUsSection); // Stop observing after animation
                }
            });
        }, { threshold: 0.5 }); // Trigger when 50% of the section is visible

        if (whyChooseUsSection) {
            observer.observe(whyChooseUsSection);
        }


        // Lightbox functionality
        const galleryImages = document.querySelectorAll('.gallery-img');
        const lightbox = document.getElementById('lightbox');
        const lightboxImg = document.getElementById('lightbox-img');
        const lightboxClose = document.querySelector('.lightbox-close');

        galleryImages.forEach(img => {
            img.addEventListener('click', () => {
                lightboxImg.src = img.getAttribute('data-large-src');
                lightbox.classList.add('active');
            });
        });

        lightboxClose.addEventListener('click', () => {
            lightbox.classList.remove('active');
        });

        lightbox.addEventListener('click', (e) => {
            if (e.target === lightbox) { // Close if clicked on overlay, not the image
                lightbox.classList.remove('active');
            }
        });

        // Form Validation
        const contactForm = document.getElementById('contact-form');
        const formMessage = document.getElementById('form-message');

        contactForm.addEventListener('submit', function(event) {
            event.preventDefault(); // Prevent default form submission

            const name = document.getElementById('name').value.trim();
            const email = document.getElementById('email').value.trim();
            const subject = document.getElementById('subject').value.trim();
            const message = document.getElementById('message').value.trim();

            if (!name || !email || !subject || !message) {
                formMessage.textContent = 'Please fill in all required fields.';
                formMessage.className = 'mt-4 p-3 rounded-lg text-center font-semibold bg-red-100 text-red-700 block';
                return;
            }

            // Simple email validation regex
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                formMessage.textContent = 'Please enter a valid email address.';
                formMessage.className = 'mt-4 p-3 rounded-lg text-center font-semibold bg-red-100 text-red-700 block';
                return;
            }

            // Simulate form submission
            console.log('Form Submitted:', { name, email, subject, message });

            formMessage.textContent = 'Thank you for your inquiry! We will get back to you soon.';
            formMessage.className = 'mt-4 p-3 rounded-lg text-center font-semibold bg-green-100 text-green-700 block';

            contactForm.reset(); // Clear the form
        });
    </script>
</body>
</html>
