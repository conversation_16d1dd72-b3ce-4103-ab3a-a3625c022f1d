<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SaaSnext Dental Studio</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* Custom styles for animations and specific elements */
        body {
            font-family: 'Inter', sans-serif;
            color: #333;
            line-height: 1.6;
        }

        /* Hero text floating animation */
        @keyframes floatText {
            0% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0); }
        }

        .float-animation {
            animation: floatText 3s ease-in-out infinite;
        }

        /* Custom scrollbar (optional, for aesthetics) */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f0f0f0;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: #1976D2;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #1565C0;
        }

        /* Style for the message box */
        .message-box {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #2196F3;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
            pointer-events: none; /* Allows clicks to pass through when hidden */
        }
        .message-box.show {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }

        /* FAQ Accordion Styling */
        .faq-item input[type="checkbox"] {
            display: none;
        }

        .faq-item label {
            cursor: pointer;
            display: block;
            padding: 1.5rem;
            background-color: #E3F2FD; /* Light blue */
            border-bottom: 1px solid #BBDEFB;
            transition: background-color 0.3s ease;
            position: relative;
            font-weight: 600;
            color: #1976D2;
            border-radius: 0.75rem; /* rounded-xl */
        }

        .faq-item label:hover {
            background-color: #BBDEFB; /* Lighter blue on hover */
        }

        .faq-item label::after {
            content: '+';
            position: absolute;
            right: 1.5rem;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.5rem;
            transition: transform 0.3s ease;
        }

        .faq-item input[type="checkbox"]:checked + label::after {
            content: '-';
            transform: translateY(-50%) rotate(0deg); /* No rotation needed for '-' */
        }

        .faq-item input[type="checkbox"]:checked + label {
            background-color: #BBDEFB; /* Keep background color consistent when open */
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
        }

        .faq-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out, padding 0.3s ease-out;
            background-color: #F8F8F8; /* Very light gray */
            padding: 0 1.5rem;
            border-bottom-left-radius: 0.75rem;
            border-bottom-right-radius: 0.75rem;
            color: #555;
        }

        .faq-item input[type="checkbox"]:checked ~ .faq-content {
            max-height: 200px; /* Adjust as needed */
            padding: 1.5rem;
        }
    </style>
</head>
<body class="bg-gray-50">

    <!-- Message Box -->
    <div id="messageBox" class="message-box"></div>

    <!-- Header & Navigation -->
    <header class="bg-white shadow-md py-4 fixed w-full z-50">
        <nav class="container mx-auto flex justify-between items-center px-4">
            <a href="#" class="text-2xl font-bold text-[#1976D2]">SaaSnext Dental</a>
            <div class="hidden md:flex space-x-6">
                <a href="#hero" class="text-gray-700 hover:text-[#2196F3] transition duration-300 rounded-md py-2 px-3">Home</a>
                <a href="#about-us" class="text-gray-700 hover:text-[#2196F3] transition duration-300 rounded-md py-2 px-3">About Us</a>
                <a href="#services" class="text-gray-700 hover:text-[#2196F3] transition duration-300 rounded-md py-2 px-3">Services</a>
                <a href="#our-team" class="text-gray-700 hover:text-[#2196F3] transition duration-300 rounded-md py-2 px-3">Our Team</a>
                <a href="#gallery" class="text-gray-700 hover:text-[#2196F3] transition duration-300 rounded-md py-2 px-3">Gallery</a>
                <a href="#testimonials" class="text-gray-700 hover:text-[#2196F3] transition duration-300 rounded-md py-2 px-3">Testimonials</a>
                <a href="#faq" class="text-gray-700 hover:text-[#2196F3] transition duration-300 rounded-md py-2 px-3">FAQ</a>
                <a href="#book-now" class="text-gray-700 hover:text-[#2196F3] transition duration-300 rounded-md py-2 px-3">Book Now</a>
            </div>
            <button id="mobile-menu-button" class="md:hidden text-gray-700 focus:outline-none">
                <i class="fas fa-bars text-2xl"></i>
            </button>
        </nav>
    </header>

    <!-- Mobile Menu Overlay -->
    <div id="mobile-menu-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden"></div>
    <div id="mobile-menu" class="fixed top-0 right-0 w-64 bg-white h-full shadow-lg z-50 transform translate-x-full transition-transform duration-300 ease-in-out">
        <div class="flex justify-end p-4">
            <button id="close-mobile-menu" class="text-gray-700 focus:outline-none">
                <i class="fas fa-times text-2xl"></i>
            </button>
        </div>
        <nav class="flex flex-col p-4 space-y-4">
            <a href="#hero" class="text-gray-700 hover:text-[#2196F3] transition duration-300 py-2" onclick="closeMobileMenu()">Home</a>
            <a href="#about-us" class="text-gray-700 hover:text-[#2196F3] transition duration-300 py-2" onclick="closeMobileMenu()">About Us</a>
            <a href="#services" class="text-gray-700 hover:text-[#2196F3] transition duration-300 py-2" onclick="closeMobileMenu()">Services</a>
            <a href="#our-team" class="text-gray-700 hover:text-[#2196F3] transition duration-300 py-2" onclick="closeMobileMenu()">Our Team</a>
            <a href="#gallery" class="text-gray-700 hover:text-[#2196F3] transition duration-300 py-2" onclick="closeMobileMenu()">Gallery</a>
            <a href="#testimonials" class="text-gray-700 hover:text-[#2196F3] transition duration-300 py-2" onclick="closeMobileMenu()">Testimonials</a>
            <a href="#faq" class="text-gray-700 hover:text-[#2196F3] transition duration-300 py-2" onclick="closeMobileMenu()">FAQ</a>
            <a href="#book-now" class="text-gray-700 hover:text-[#2196F3] transition duration-300 py-2" onclick="closeMobileMenu()">Book Now</a>
        </nav>
    </div>

    <!-- Main Content -->
    <main class="pt-16">
        <!-- Hero Section -->
        <section id="hero" class="relative bg-cover bg-center h-screen flex items-center justify-center text-white" style="background-image: url('https://images.unsplash.com/photo-1596499318182-e2ff06757f12?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D');">
            <div class="absolute inset-0 bg-gradient-to-r from-[#1976D2] to-[#BBDEFB] opacity-70"></div>
            <div class="relative z-10 text-center px-4">
                <h1 class="text-5xl md:text-7xl font-extrabold leading-tight float-animation drop-shadow-lg">
                    Your Journey to a <br><span class="text-[#E0F7FA]">Brighter Smile</span> Starts Here
                </h1>
                <p class="mt-6 text-xl md:text-2xl font-light float-animation animation-delay-500">
                    Experience compassionate and advanced dental care.
                </p>
                <a href="#book-now" class="mt-10 inline-block bg-[#4CAF50] hover:bg-[#388E3C] text-white font-bold py-4 px-8 rounded-full shadow-lg transform hover:scale-105 transition duration-300 ease-in-out">
                    Book Your Appointment
                </a>
            </div>
        </section>

        <!-- About Us Section -->
        <section id="about-us" class="py-16 bg-white">
            <div class="container mx-auto px-4 flex flex-col lg:flex-row items-center gap-12">
                <div class="lg:w-1/2">
                    <img src="https://images.unsplash.com/photo-1579126038374-6064e93716a7?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Modern Dental Clinic Interior" class="rounded-xl shadow-xl w-full h-auto object-cover">
                </div>
                <div class="lg:w-1/2 text-center lg:text-left">
                    <h2 class="text-4xl font-bold text-[#1976D2] mb-6">About SaaSnext Dental Studio</h2>
                    <p class="text-gray-700 text-lg mb-4">
                        At SaaSnext Dental Studio, we are dedicated to providing the highest quality dental care in a comfortable and welcoming environment. Our state-of-the-art facility is equipped with the latest technology, ensuring precise diagnoses and effective treatments. We believe in a patient-centered approach, focusing on your individual needs and creating personalized treatment plans.
                    </p>
                    <p class="text-gray-700 text-lg">
                        Our mission is to help you achieve and maintain a healthy, beautiful smile for a lifetime. From routine check-ups to complex restorative procedures, our experienced team is here to guide you every step of the way with compassion and expertise.
                    </p>
                </div>
            </div>
        </section>

        <!-- Services Section -->
        <section id="services" class="py-16 bg-gray-100">
            <div class="container mx-auto px-4 text-center">
                <h2 class="text-4xl font-bold text-[#1976D2] mb-12">Our Services</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Service Card 1 -->
                    <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 group">
                        <div class="text-5xl text-[#2196F3] mb-6"><i class="fas fa-tooth group-hover:animate-bounce"></i></div>
                        <h3 class="text-2xl font-semibold text-gray-800 mb-4">Professional Whitening</h3>
                        <p class="text-gray-600">Achieve a dazzling, brighter smile with our safe and effective whitening treatments.</p>
                    </div>
                    <!-- Service Card 2 -->
                    <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 group">
                        <div class="text-5xl text-[#2196F3] mb-6"><i class="fas fa-orthodontics group-hover:animate-bounce"></i></div>
                        <h3 class="text-2xl font-semibold text-gray-800 mb-4">Braces & Aligners</h3>
                        <p class="text-gray-600">Straighten your teeth comfortably with traditional braces or clear aligners.</p>
                    </div>
                    <!-- Service Card 3 -->
                    <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 group">
                        <div class="text-5xl text-[#2196F3] mb-6"><i class="fas fa-dental-implant group-hover:animate-bounce"></i></div>
                        <h3 class="text-2xl font-semibold text-gray-800 mb-4">Dental Implants</h3>
                        <p class="text-gray-600">Restore missing teeth with durable and natural-looking dental implants.</p>
                    </div>
                    <!-- Service Card 4 -->
                    <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 group">
                        <div class="text-5xl text-[#2196F3] mb-6"><i class="fas fa-fill-drip group-hover:animate-bounce"></i></div>
                        <h3 class="text-2xl font-semibold text-gray-800 mb-4">Root Canal Therapy</h3>
                        <p class="text-gray-600">Save damaged teeth and alleviate pain with our expert root canal treatments.</p>
                    </div>
                    <!-- Service Card 5 -->
                    <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 group">
                        <div class="text-5xl text-[#2196F3] mb-6"><i class="fas fa-crown group-hover:animate-bounce"></i></div>
                        <h3 class="text-2xl font-semibold text-gray-800 mb-4">Crowns & Bridges</h3>
                        <p class="text-gray-600">Repair and replace damaged or missing teeth with custom crowns and bridges.</p>
                    </div>
                    <!-- Service Card 6 -->
                    <div class="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 group">
                        <div class="text-5xl text-[#2196F3] mb-6"><i class="fas fa-user-shield group-hover:animate-bounce"></i></div>
                        <h3 class="text-2xl font-semibold text-gray-800 mb-4">Preventive Care</h3>
                        <p class="text-gray-600">Maintain optimal oral health with regular check-ups, cleanings, and fluoride treatments.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Our Team Section -->
        <section id="our-team" class="py-16 bg-white">
            <div class="container mx-auto px-4 text-center">
                <h2 class="text-4xl font-bold text-[#1976D2] mb-12">Meet Our Expert Team</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <!-- Team Member 1 -->
                    <div class="bg-gray-100 p-6 rounded-xl shadow-lg flex flex-col items-center">
                        <img src="https://images.unsplash.com/photo-1622252199049-ce271ac86ac8?q=80&w=2800&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Dr. Sarah Lee" class="w-32 h-32 rounded-full object-cover mb-4 border-4 border-[#2196F3]">
                        <h3 class="text-xl font-semibold text-gray-800">Dr. Sarah Lee</h3>
                        <p class="text-gray-600">Lead Dentist</p>
                        <p class="text-gray-500 text-sm mt-2">Specializing in cosmetic dentistry and restorative procedures.</p>
                    </div>
                    <!-- Team Member 2 -->
                    <div class="bg-gray-100 p-6 rounded-xl shadow-lg flex flex-col items-center">
                        <img src="https://images.unsplash.com/photo-1588776816003-34bd430a6e7c?q=80&w=2866&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Dr. Mark Johnson" class="w-32 h-32 rounded-full object-cover mb-4 border-4 border-[#2196F3]">
                        <h3 class="text-xl font-semibold text-gray-800">Dr. Mark Johnson</h3>
                        <p class="text-gray-600">Orthodontist</p>
                        <p class="text-gray-500 text-sm mt-2">Expert in braces, Invisalign, and clear aligner treatments.</p>
                    </div>
                    <!-- Team Member 3 -->
                    <div class="bg-gray-100 p-6 rounded-xl shadow-lg flex flex-col items-center">
                        <img src="https://images.unsplash.com/photo-1623910350436-1e0e84b9f291?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Dr. Lisa Chen" class="w-32 h-32 rounded-full object-cover mb-4 border-4 border-[#2196F3]">
                        <h3 class="text-xl font-semibold text-gray-800">Dr. Lisa Chen</h3>
                        <p class="text-gray-600">Periodontist</p>
                        <p class="text-gray-500 text-sm mt-2">Specializes in gum health, dental implants, and bone grafting.</p>
                    </div>
                    <!-- Team Member 4 -->
                    <div class="bg-gray-100 p-6 rounded-xl shadow-lg flex flex-col items-center">
                        <img src="https://images.unsplash.com/photo-1622902046580-c65059d4c794?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Dr. David Miller" class="w-32 h-32 rounded-full object-cover mb-4 border-4 border-[#2196F3]">
                        <h3 class="text-xl font-semibold text-gray-800">Dr. David Miller</h3>
                        <p class="text-gray-600">Dental Hygienist</p>
                        <p class="text-gray-500 text-sm mt-2">Dedicated to preventive care and patient education.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Gallery Section -->
        <section id="gallery" class="py-16 bg-white">
            <div class="container mx-auto px-4 text-center">
                <h2 class="text-4xl font-bold text-[#1976D2] mb-12">Before & After Gallery</h2>
                <div class="relative max-w-4xl mx-auto bg-gray-100 rounded-xl shadow-lg overflow-hidden">
                    <img id="gallery-image" src="https://images.unsplash.com/photo-1605330310237-4c4f03a6c117?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Before Smile 1" class="w-full h-auto object-cover rounded-t-xl transition-opacity duration-500 ease-in-out">

                    <div class="absolute inset-0 flex items-center justify-between p-4">
                        <button id="prev-gallery" class="bg-[#1976D2] text-white p-3 rounded-full shadow-md hover:bg-[#1565C0] focus:outline-none transition-all duration-300 transform hover:scale-110">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button id="next-gallery" class="bg-[#1976D2] text-white p-3 rounded-full shadow-md hover:bg-[#1565C0] focus:outline-none transition-all duration-300 transform hover:scale-110">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>

                    <div id="gallery-dots" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                        <!-- Dots will be generated by JS -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Testimonials Section -->
        <section id="testimonials" class="py-16 bg-gray-100">
            <div class="container mx-auto px-4 text-center">
                <h2 class="text-4xl font-bold text-[#1976D2] mb-12">What Our Patients Say</h2>
                <div class="relative max-w-3xl mx-auto overflow-hidden">
                    <div id="testimonial-carousel" class="flex transition-transform duration-700 ease-in-out">
                        <!-- Testimonial Cards will be dynamically loaded by JS -->
                    </div>

                    <div class="absolute inset-0 flex items-center justify-between p-4">
                        <button id="prev-testimonial" class="bg-[#1976D2] text-white p-3 rounded-full shadow-md hover:bg-[#1565C0] focus:outline-none transition-all duration-300 transform hover:scale-110">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button id="next-testimonial" class="bg-[#1976D2] text-white p-3 rounded-full shadow-md hover:bg-[#1565C0] focus:outline-none transition-all duration-300 transform hover:scale-110">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>

                    <div id="testimonial-dots" class="flex justify-center mt-8 space-x-2">
                        <!-- Testimonial Dots will be dynamically loaded by JS -->
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section id="faq" class="py-16 bg-white">
            <div class="container mx-auto px-4 text-center">
                <h2 class="text-4xl font-bold text-[#1976D2] mb-12">Frequently Asked Questions</h2>
                <div class="max-w-2xl mx-auto space-y-4">
                    <!-- FAQ Item 1 -->
                    <div class="faq-item shadow-md rounded-xl overflow-hidden">
                        <input type="checkbox" id="faq1">
                        <label for="faq1">What payment options do you offer?</label>
                        <div class="faq-content">
                            <p class="py-4">We accept most major credit cards, debit cards, and offer flexible payment plans. Please contact our office for more details on insurance and financing options.</p>
                        </div>
                    </div>
                    <!-- FAQ Item 2 -->
                    <div class="faq-item shadow-md rounded-xl overflow-hidden">
                        <input type="checkbox" id="faq2">
                        <label for="faq2">How often should I visit the dentist?</label>
                        <div class="faq-content">
                            <p class="py-4">Generally, we recommend visiting for a check-up and cleaning every six months. However, your individual needs may vary, and we can advise you on a personalized schedule.</p>
                        </div>
                    </div>
                    <!-- FAQ Item 3 -->
                    <div class="faq-item shadow-md rounded-xl overflow-hidden">
                        <input type="checkbox" id="faq3">
                        <label for="faq3">Do you offer emergency dental services?</label>
                        <div class="faq-content">
                            <p class="py-4">Yes, we provide emergency dental care for urgent situations. Please call our office immediately if you are experiencing a dental emergency.</p>
                        </div>
                    </div>
                    <!-- FAQ Item 4 -->
                    <div class="faq-item shadow-md rounded-xl overflow-hidden">
                        <input type="checkbox" id="faq4">
                        <label for="faq4">What should I do if I miss an appointment?</label>
                        <div class="faq-content">
                            <p class="py-4">We understand that unforeseen circumstances can arise. Please notify us as soon as possible if you need to reschedule or cancel an appointment. A cancellation fee may apply if not notified within 24 hours.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>


        <!-- Book Now Section -->
        <section id="book-now" class="py-16 bg-gray-100">
            <div class="container mx-auto px-4 text-center">
                <h2 class="text-4xl font-bold text-[#1976D2] mb-12">Book Your Appointment</h2>
                <div class="max-w-xl mx-auto bg-white p-8 rounded-xl shadow-lg">
                    <p class="text-gray-700 mb-6 text-lg">Ready for a healthier, brighter smile? Fill out the form below or contact us directly.</p>
                    <form id="booking-form" class="space-y-6">
                        <div>
                            <input type="text" id="name" name="name" placeholder="Your Full Name" required class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2196F3] focus:border-transparent transition duration-200">
                        </div>
                        <div>
                            <input type="email" id="email" name="email" placeholder="Your Email Address" required class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2196F3] focus:border-transparent transition duration-200">
                        </div>
                        <div>
                            <input type="tel" id="phone" name="phone" placeholder="Your Phone Number" class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2196F3] focus:border-transparent transition duration-200">
                        </div>
                        <div>
                            <textarea id="message" name="message" rows="5" placeholder="Your Message or Preferred Service" class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2196F3] focus:border-transparent transition duration-200"></textarea>
                        </div>
                        <button type="submit" class="w-full bg-[#2196F3] hover:bg-[#1976D2] text-white font-bold py-4 px-6 rounded-full shadow-md transform hover:scale-105 transition duration-300 ease-in-out">
                            Submit Appointment Request
                        </button>
                    </form>

                    <div class="mt-8 flex flex-col sm:flex-row justify-center gap-4">
                        <a href="https://wa.me/1234567890?text=Hello%2C%20I%20would%20like%20to%20book%20an%20appointment." target="_blank" class="inline-flex items-center justify-center bg-[#25D366] hover:bg-[#1DA851] text-white font-bold py-3 px-6 rounded-full shadow-md transform hover:scale-105 transition duration-300 ease-in-out">
                            <i class="fab fa-whatsapp mr-2 text-xl"></i> Chat on WhatsApp
                        </a>
                        <a href="mailto:<EMAIL>" class="inline-flex items-center justify-center bg-[#EA4335] hover:bg-[#D43F2E] text-white font-bold py-3 px-6 rounded-full shadow-md transform hover:scale-105 transition duration-300 ease-in-out">
                            <i class="fas fa-envelope mr-2 text-xl"></i> Email Us
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-[#1976D2] text-white py-8">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2024 SaaSnext Dental Studio. All rights reserved.</p>
            <div class="mt-4 space-x-4">
                <a href="#" class="text-white hover:text-[#BBDEFB] transition duration-300"><i class="fab fa-facebook-f"></i></a>
                <a href="#" class="text-white hover:text-[#BBDEFB] transition duration-300"><i class="fab fa-twitter"></i></a>
                <a href="#" class="text-white hover:text-[#BBDEFB] transition duration-300"><i class="fab fa-instagram"></i></a>
                <a href="#" class="text-white hover:text-[#BBDEFB] transition duration-300"><i class="fab fa-linkedin-in"></i></a>
            </div>
        </div>
    </footer>

    <script>
        // --- Message Box Functionality ---
        function showMessageBox(message, duration = 3000) {
            const messageBox = document.getElementById('messageBox');
            messageBox.textContent = message;
            messageBox.classList.add('show');
            setTimeout(() => {
                messageBox.classList.remove('show');
            }, duration);
        }

        // --- Mobile Menu Toggle ---
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
        const closeMobileMenuButton = document.getElementById('close-mobile-menu');

        function openMobileMenu() {
            mobileMenu.classList.remove('translate-x-full');
            mobileMenuOverlay.classList.remove('hidden');
        }

        function closeMobileMenu() {
            mobileMenu.classList.add('translate-x-full');
            mobileMenuOverlay.classList.add('hidden');
        }

        mobileMenuButton.addEventListener('click', openMobileMenu);
        closeMobileMenuButton.addEventListener('click', closeMobileMenu);
        mobileMenuOverlay.addEventListener('click', closeMobileMenu); // Close when clicking outside

        // --- Gallery Slider Logic ---
        const galleryImages = [
            { src: 'https://images.unsplash.com/photo-1605330310237-4c4f03a6c117?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D', alt: 'Before Smile 1' }, // Before - stained teeth
            { src: 'https://images.unsplash.com/photo-1596499318182-e2ff06757f12?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D', alt: 'After Smile 1' },  // After - bright smile
            { src: 'https://images.unsplash.com/photo-1600021675001-382a9d2243d5?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D', alt: 'Before Braces' }, // Before - misaligned teeth
            { src: 'https://images.unsplash.com/photo-1596499318182-e2ff06757f12?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D', alt: 'After Braces' }, // After - straightened teeth (re-using bright smile)
            { src: 'https://images.unsplash.com/photo-1596499318182-e2ff06757f12?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D', alt: 'Before Implant' }, // Before - missing tooth
            { src: 'https://images.unsplash.com/photo-1596499318182-e2ff06757f12?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D', alt: 'After Implant' }  // After - restored tooth
        ];
        let currentGalleryIndex = 0;
        const galleryImageElement = document.getElementById('gallery-image');
        const prevGalleryButton = document.getElementById('prev-gallery');
        const nextGalleryButton = document.getElementById('next-gallery');
        const galleryDotsContainer = document.getElementById('gallery-dots');

        function updateGalleryImage() {
            galleryImageElement.style.opacity = 0; // Fade out
            setTimeout(() => {
                galleryImageElement.src = galleryImages[currentGalleryIndex].src;
                galleryImageElement.alt = galleryImages[currentGalleryIndex].alt;
                galleryImageElement.style.opacity = 1; // Fade in
            }, 200); // Match this duration with CSS transition time

            updateGalleryDots();
        }

        function createGalleryDots() {
            galleryDotsContainer.innerHTML = '';
            galleryImages.forEach((_, index) => {
                const dot = document.createElement('button');
                dot.classList.add('w-3', 'h-3', 'rounded-full', 'bg-gray-400', 'hover:bg-[#2196F3]', 'transition-colors', 'duration-300');
                dot.addEventListener('click', () => {
                    currentGalleryIndex = index;
                    updateGalleryImage();
                });
                galleryDotsContainer.appendChild(dot);
            });
            updateGalleryDots();
        }

        function updateGalleryDots() {
            const dots = galleryDotsContainer.children;
            Array.from(dots).forEach((dot, index) => {
                if (index === currentGalleryIndex) {
                    dot.classList.remove('bg-gray-400');
                    dot.classList.add('bg-[#2196F3]');
                } else {
                    dot.classList.remove('bg-[#2196F3]');
                    dot.classList.add('bg-gray-400');
                }
            });
        }

        prevGalleryButton.addEventListener('click', () => {
            currentGalleryIndex = (currentGalleryIndex === 0) ? galleryImages.length - 1 : currentGalleryIndex - 1;
            updateGalleryImage();
        });

        nextGalleryButton.addEventListener('click', () => {
            currentGalleryIndex = (currentGalleryIndex === galleryImages.length - 1) ? 0 : currentGalleryIndex + 1;
            updateGalleryImage();
        });

        // Initialize gallery
        createGalleryDots();
        updateGalleryImage();


        // --- Testimonials Carousel Logic ---
        const testimonials = [
            {
                quote: "SaaSnext Dental Studio provided the best dental experience I've ever had. The staff are incredibly kind and the results are amazing!",
                author: "Jane Doe",
                title: "Happy Patient",
                avatar: "https://placehold.co/80x80/BBDEFB/FFFFFF?text=JD"
            },
            {
                quote: "I used to be so anxious about dental visits, but the team here made me feel completely at ease. My new smile is perfect!",
                author: "John Smith",
                title: "Satisfied Customer",
                avatar: "https://placehold.co/80x80/B2EBF2/FFFFFF?text=JS"
            },
            {
                quote: "Professional, efficient, and genuinely caring. I highly recommend SaaSnext Dental for anyone seeking top-notch dental care.",
                author: "Emily White",
                title: "Regular Patient",
                avatar: "https://placehold.co/80x80/BBDEFB/FFFFFF?text=EW"
            },
            {
                quote: "From whitening to general check-ups, every service has been exceptional. My teeth have never looked better!",
                author: "Michael Brown",
                title: "Valued Client",
                avatar: "https://placehold.co/80x80/B2EBF2/FFFFFF?text=MB"
            }
        ];

        let currentTestimonialIndex = 0;
        const testimonialCarousel = document.getElementById('testimonial-carousel');
        const prevTestimonialButton = document.getElementById('prev-testimonial');
        const nextTestimonialButton = document.getElementById('next-testimonial');
        const testimonialDotsContainer = document.getElementById('testimonial-dots');

        function loadTestimonials() {
            testimonialCarousel.innerHTML = ''; // Clear existing content
            testimonials.forEach((testimonial, index) => {
                const testimonialCard = document.createElement('div');
                testimonialCard.classList.add(
                    'w-full', 'flex-shrink-0', 'p-8', 'bg-white', 'rounded-xl', 'shadow-lg', 'text-center',
                    'transition-all', 'duration-700', 'ease-in-out', 'transform',
                    'flex', 'flex-col', 'items-center', 'justify-center', 'min-h-[250px]' /* Ensure consistent height */
                );
                // Dynamically apply hidden class based on index
                if (index !== currentTestimonialIndex) {
                    testimonialCard.classList.add('hidden');
                }

                testimonialCard.innerHTML = `
                    <p class="text-lg md:text-xl text-gray-700 italic mb-6">"${testimonial.quote}"</p>
                    <img src="${testimonial.avatar}" alt="${testimonial.author}" class="w-20 h-20 rounded-full mx-auto mb-4 object-cover border-4 border-[#2196F3]">
                    <p class="text-xl font-semibold text-gray-800">${testimonial.author}</p>
                    <p class="text-gray-500 text-sm">${testimonial.title}</p>
                `;
                testimonialCarousel.appendChild(testimonialCard);
            });
            updateTestimonialDisplay();
            createTestimonialDots();
        }

        function updateTestimonialDisplay() {
            // Hide all testimonials first
            Array.from(testimonialCarousel.children).forEach(card => card.classList.add('hidden'));
            // Show the current testimonial
            testimonialCarousel.children[currentTestimonialIndex].classList.remove('hidden');

            updateTestimonialDots();
        }

        function createTestimonialDots() {
            testimonialDotsContainer.innerHTML = '';
            testimonials.forEach((_, index) => {
                const dot = document.createElement('button');
                dot.classList.add('w-3', 'h-3', 'rounded-full', 'bg-gray-400', 'hover:bg-[#2196F3]', 'transition-colors', 'duration-300');
                dot.addEventListener('click', () => {
                    currentTestimonialIndex = index;
                    updateTestimonialDisplay();
                });
                testimonialDotsContainer.appendChild(dot);
            });
            updateTestimonialDots();
        }

        function updateTestimonialDots() {
            const dots = testimonialDotsContainer.children;
            Array.from(dots).forEach((dot, index) => {
                if (index === currentTestimonialIndex) {
                    dot.classList.remove('bg-gray-400');
                    dot.classList.add('bg-[#2196F3]');
                } else {
                    dot.classList.remove('bg-[#2196F3]');
                    dot.classList.add('bg-gray-400');
                }
            });
        }

        prevTestimonialButton.addEventListener('click', () => {
            currentTestimonialIndex = (currentTestimonialIndex === 0) ? testimonials.length - 1 : currentTestimonialIndex - 1;
            updateTestimonialDisplay();
        });

        nextTestimonialButton.addEventListener('click', () => {
            currentTestimonialIndex = (currentTestimonialIndex === testimonials.length - 1) ? 0 : currentTestimonialIndex + 1;
            updateTestimonialDisplay();
        });

        // Initialize testimonials
        loadTestimonials();
        // Auto-advance testimonials
        setInterval(() => {
            currentTestimonialIndex = (currentTestimonialIndex === testimonials.length - 1) ? 0 : currentTestimonialIndex + 1;
            updateTestimonialDisplay();
        }, 5000); // Change every 5 seconds


        // --- Booking Form Submission (Client-side simulation) ---
        const bookingForm = document.getElementById('booking-form');
        bookingForm.addEventListener('submit', function(event) {
            event.preventDefault(); // Prevent default form submission

            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const phone = document.getElementById('phone').value;
            const message = document.getElementById('message').value;

            // In a real application, you would send this data to a server
            console.log('Booking Request Submitted:');
            console.log('Name:', name);
            console.log('Email:', email);
            console.log('Phone:', phone);
            console.log('Message:', message);

            showMessageBox('Thank you for your appointment request! We will contact you soon.');

            // Clear the form
            bookingForm.reset();
        });

        // Add a general check for Font Awesome and other third-party scripts.
        // This is a basic example; more robust checks might be needed for production.
        window.addEventListener('load', () => {
            if (typeof FontAwesomeConfig === 'undefined') {
                console.warn('Font Awesome might not be loaded correctly. Check CDN link.');
            }
        });

    </script>
</body>
</html>
