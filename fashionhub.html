<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fashion Hub</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- AOS CSS CDN -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* Custom styles for animations and specific elements */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        body {
            font-family: 'Inter', sans-serif;
            scroll-behavior: smooth; /* Smooth scrolling for anchor links */
        }

        /* Sticky Header */
        .header-sticky {
            position: fixed;
            top: 0;
            width: 100%;
            background-color: rgba(255, 255, 255, 0.95);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            z-index: 1000;
            transition: all 0.3s ease-in-out;
        }

        /* Product Hover Zoom Effect */
        .product-card img, .category-card img {
            transition: transform 0.3s ease-in-out;
        }

        .product-card:hover img, .category-card:hover img {
            transform: scale(1.05);
        }

        /* Hero Text Animation */
        .animated-text span {
            display: inline-block;
            opacity: 0;
            transform: translateY(20px);
            animation: slideInUp 0.8s forwards;
            animation-delay: var(--delay);
        }

        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Quick View Modal */
        .modal-overlay {
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            animation: fadeInScale 0.3s ease-out;
        }

        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* Testimonial Slider Navigation Dots */
        .slider-dots button {
            transition: background-color 0.3s ease;
        }

        .slider-dots button.active {
            background-color: #6366f1; /* Indigo 500 */
        }

        /* Newsletter input focus */
        .newsletter-input:focus {
            outline: none;
            border-color: #6366f1; /* Indigo 500 */
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3); /* Indigo 500 with opacity */
        }

        /* Custom scrollbar for better aesthetics */
        ::-webkit-scrollbar {
            width: 10px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">

    <!-- Header Section -->
    <header id="main-header" class="bg-white py-4 px-6 md:px-12 fixed top-0 left-0 w-full z-50 transition-all duration-300 ease-in-out shadow-sm">
        <div class="container mx-auto flex justify-between items-center">
            <!-- Logo -->
            <a href="#" class="text-3xl font-bold text-indigo-600 rounded-lg p-2 hover:bg-indigo-50 transition-colors duration-200">Fashion Hub</a>

            <!-- Navigation Links (Hidden on mobile, shown on desktop) -->
            <nav class="hidden md:block">
                <ul class="flex space-x-8">
                    <li><a href="#shop" class="text-lg font-medium text-gray-700 hover:text-indigo-600 transition-colors duration-200 rounded-md py-2 px-3 hover:bg-gray-100">Shop</a></li>
                    <li><a href="#new-arrivals" class="text-lg font-medium text-gray-700 hover:text-indigo-600 transition-colors duration-200 rounded-md py-2 px-3 hover:bg-gray-100">New Arrivals</a></li>
                    <li><a href="#categories" class="text-lg font-medium text-gray-700 hover:text-indigo-600 transition-colors duration-200 rounded-md py-2 px-3 hover:bg-gray-100">Categories</a></li>
                    <li><a href="#about" class="text-lg font-medium text-gray-700 hover:text-indigo-600 transition-colors duration-200 rounded-md py-2 px-3 hover:bg-gray-100">About</a></li>
                    <li><a href="#contact" class="text-lg font-medium text-gray-700 hover:text-indigo-600 transition-colors duration-200 rounded-md py-2 px-3 hover:bg-gray-100">Contact</a></li>
                </ul>
            </nav>

            <!-- Mobile Menu Button (Hidden on desktop, shown on mobile) -->
            <button id="mobile-menu-button" class="md:hidden text-gray-700 hover:text-indigo-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 rounded-md p-2">
                <i class="fas fa-bars text-2xl"></i>
            </button>
        </div>

        <!-- Mobile Navigation Menu (Hidden by default) -->
        <nav id="mobile-menu" class="md:hidden hidden absolute top-full left-0 w-full bg-white shadow-lg py-4 border-t border-gray-200">
            <ul class="flex flex-col items-center space-y-4">
                <li><a href="#shop" class="text-lg font-medium text-gray-700 hover:text-indigo-600 transition-colors duration-200 rounded-md py-2 px-3 w-full block text-center" onclick="closeMobileMenu()">Shop</a></li>
                <li><a href="#new-arrivals" class="text-lg font-medium text-gray-700 hover:text-indigo-600 transition-colors duration-200 rounded-md py-2 px-3 w-full block text-center" onclick="closeMobileMenu()">New Arrivals</a></li>
                <li><a href="#categories" class="text-lg font-medium text-gray-700 hover:text-indigo-600 transition-colors duration-200 rounded-md py-2 px-3 w-full block text-center" onclick="closeMobileMenu()">Categories</a></li>
                <li><a href="#about" class="text-lg font-medium text-gray-700 hover:text-indigo-600 transition-colors duration-200 rounded-md py-2 px-3 w-full block text-center" onclick="closeMobileMenu()">About</a></li>
                <li><a href="#contact" class="text-lg font-medium text-gray-700 hover:text-indigo-600 transition-colors duration-200 rounded-md py-2 px-3 w-full block text-center" onclick="closeMobileMenu()">Contact</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero Section -->
    <section id="hero" class="relative bg-gradient-to-r from-purple-500 to-indigo-600 text-white min-h-screen flex items-center justify-center p-8 overflow-hidden">
        <div class="container mx-auto text-center z-10">
            <h1 class="text-5xl md:text-7xl font-extrabold mb-6 animated-text leading-tight">
                <span style="--delay: 0s;">Unleash</span>
                <span style="--delay: 0.2s;">Your</span>
                <span style="--delay: 0.4s;">Style</span>
                <span style="--delay: 0.6s;">with</span>
                <span style="--delay: 0.8s;">Fashion</span>
                <span style="--delay: 1s;">Hub</span>
            </h1>
            <p class="text-xl md:text-2xl mb-10 max-w-2xl mx-auto animated-text" style="--delay: 1.2s;" data-aos="fade-up" data-aos-delay="1200">Discover the latest trends and timeless classics that define your unique look.</p>
            <div data-aos="fade-up" data-aos-delay="1400">
                <a href="#shop" class="bg-white text-indigo-700 hover:bg-indigo-100 font-bold py-4 px-8 rounded-full text-lg shadow-lg transform hover:scale-105 transition-all duration-300 ease-in-out">Shop Now</a>
            </div>
        </div>
        <!-- Background elements for visual appeal -->
        <div class="absolute inset-0 z-0">
            <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-indigo-700 opacity-20 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
            <div class="absolute top-1/2 right-1/4 w-80 h-80 bg-purple-700 opacity-20 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
            <div class="absolute bottom-1/4 left-1/3 w-72 h-72 bg-blue-700 opacity-20 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
        </div>
    </section>

    <!-- Featured Products Section -->
    <section id="shop" class="py-16 md:py-24 bg-gray-100">
        <div class="container mx-auto px-6 md:px-12">
            <h2 class="text-4xl md:text-5xl font-extrabold text-center text-gray-900 mb-12" data-aos="fade-up">Featured Products</h2>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Product Card 1 -->
                <div class="product-card bg-white rounded-xl shadow-lg overflow-hidden transform hover:-translate-y-2 transition-all duration-300 ease-in-out" data-aos="fade-up" data-aos-delay="100">
                    <img src="https://placehold.co/600x400/FFDDC1/333333?text=Elegant+Dress" alt="Elegant Dress" class="w-full h-64 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Elegant Evening Dress</h3>
                        <p class="text-gray-600 mb-4">A stunning dress for special occasions.</p>
                        <div class="flex justify-between items-center">
                            <span class="text-2xl font-bold text-indigo-600">₹10,000</span>
                            <button class="bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors duration-200 text-sm font-medium quick-view-btn" data-product='{"name": "Elegant Evening Dress", "price": "₹10,000", "description": "A stunning dress designed for special occasions, offering comfort and style.", "image": "https://placehold.co/600x400/FFDDC1/333333?text=Elegant+Dress"}'>Quick View</button>
                        </div>
                    </div>
                </div>

                <!-- Product Card 2 -->
                <div class="product-card bg-white rounded-xl shadow-lg overflow-hidden transform hover:-translate-y-2 transition-all duration-300 ease-in-out" data-aos="fade-up" data-aos-delay="200">
                    <img src="https://placehold.co/600x400/C1FFDD/333333?text=Casual+Shirt" alt="Casual Shirt" class="w-full h-64 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Comfort Fit Casual Shirt</h3>
                        <p class="text-gray-600 mb-4">Perfect for everyday wear, stylish and breathable.</p>
                        <div class="flex justify-between items-center">
                            <span class="text-2xl font-bold text-indigo-600">₹1,500</span>
                            <button class="bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors duration-200 text-sm font-medium quick-view-btn" data-product='{"name": "Comfort Fit Casual Shirt", "price": "₹1,500", "description": "A versatile shirt designed for everyday comfort and style, made from breathable fabric.", "image": "https://placehold.co/600x400/C1FFDD/333333?text=Casual+Shirt"}'>Quick View</button>
                        </div>
                    </div>
                </div>

                <!-- Product Card 3 -->
                <div class="product-card bg-white rounded-xl shadow-lg overflow-hidden transform hover:-translate-y-2 transition-all duration-300 ease-in-out" data-aos="fade-up" data-aos-delay="300">
                    <img src="https://placehold.co/600x400/DDC1FF/333333?text=Denim+Jeans" alt="Denim Jeans" class="w-full h-64 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Classic Slim-Fit Jeans</h3>
                        <p class="text-gray-600 mb-4">Timeless denim, designed for a perfect fit.</p>
                        <div class="flex justify-between items-center">
                            <span class="text-2xl font-bold text-indigo-600">₹2,500</span>
                            <button class="bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors duration-200 text-sm font-medium quick-view-btn" data-product='{"name": "Classic Slim-Fit Jeans", "price": "₹2,500", "description": "Durable and stylish denim jeans with a timeless slim-fit design for everyday wear.", "image": "https://placehold.co/600x400/DDC1FF/333333?text=Denim+Jeans"}'>Quick View</button>
                        </div>
                    </div>
                </div>

                <!-- Product Card 4 -->
                <div class="product-card bg-white rounded-xl shadow-lg overflow-hidden transform hover:-translate-y-2 transition-all duration-300 ease-in-out" data-aos="fade-up" data-aos-delay="400">
                    <img src="https://placehold.co/600x400/C1FFCC/333333?text=Summer+Skirt" alt="Summer Skirt" class="w-full h-64 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Flowy Summer Skirt</h3>
                        <p class="text-gray-600 mb-4">Lightweight and comfortable, perfect for sunny days.</p>
                        <div class="flex justify-between items-center">
                            <span class="text-2xl font-bold text-indigo-600">₹1,800</span>
                            <button class="bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors duration-200 text-sm font-medium quick-view-btn" data-product='{"name": "Flowy Summer Skirt", "price": "₹1,800", "description": "A light and airy skirt, ideal for warm weather, offering a comfortable and chic look.", "image": "https://placehold.co/600x400/C1FFCC/333333?text=Summer+Skirt"}'>Quick View</button>
                        </div>
                    </div>
                </div>

                <!-- Product Card 5 -->
                <div class="product-card bg-white rounded-xl shadow-lg overflow-hidden transform hover:-translate-y-2 transition-all duration-300 ease-in-out" data-aos="fade-up" data-aos-delay="500">
                    <img src="https://placehold.co/600x400/FFC1C1/333333?text=Leather+Jacket" alt="Leather Jacket" class="w-full h-64 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Premium Leather Jacket</h3>
                        <p class="text-gray-600 mb-4">Stylish and durable, a wardrobe essential.</p>
                        <div class="flex justify-between items-center">
                            <span class="text-2xl font-bold text-indigo-600">₹8,500</span>
                            <button class="bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors duration-200 text-sm font-medium quick-view-btn" data-product='{"name": "Premium Leather Jacket", "price": "₹8,500", "description": "A classic leather jacket crafted for durability and timeless style, perfect for any season.", "image": "https://placehold.co/600x400/FFC1C1/333333?text=Leather+Jacket"}'>Quick View</button>
                        </div>
                    </div>
                </div>

                <!-- Product Card 6 -->
                <div class="product-card bg-white rounded-xl shadow-lg overflow-hidden transform hover:-translate-y-2 transition-all duration-300 ease-in-out" data-aos="fade-up" data-aos-delay="600">
                    <img src="https://placehold.co/600x400/CCE0FF/333333?text=Sports+Shoes" alt="Sports Shoes" class="w-full h-64 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Athletic Sports Shoes</h3>
                        <p class="text-gray-600 mb-4">Designed for comfort and performance.</p>
                        <div class="flex justify-between items-center">
                            <span class="text-2xl font-bold text-indigo-600">₹3,000</span>
                            <button class="bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors duration-200 text-sm font-medium quick-view-btn" data-product='{"name": "Athletic Sports Shoes", "price": "₹3,000", "description": "High-performance sports shoes engineered for maximum comfort and support during any activity.", "image": "https://placehold.co/600x400/CCE0FF/333333?text=Sports+Shoes"}'>Quick View</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- New Arrivals Section -->
    <section id="new-arrivals" class="py-16 md:py-24 bg-white">
        <div class="container mx-auto px-6 md:px-12">
            <h2 class="text-4xl md:text-5xl font-extrabold text-center text-gray-900 mb-12" data-aos="fade-up">New Arrivals</h2>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- New Product Card 1 -->
                <div class="product-card bg-gray-50 rounded-xl shadow-lg overflow-hidden transform hover:-translate-y-2 transition-all duration-300 ease-in-out" data-aos="fade-up" data-aos-delay="100">
                    <img src="https://placehold.co/600x400/E1C1FF/333333?text=Boho+Maxi+Dress" alt="Boho Maxi Dress" class="w-full h-64 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Boho Chic Maxi Dress</h3>
                        <p class="text-gray-600 mb-4">Embrace your free spirit with this stylish maxi.</p>
                        <div class="flex justify-between items-center">
                            <span class="text-2xl font-bold text-indigo-600">₹3,800</span>
                            <button class="bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors duration-200 text-sm font-medium quick-view-btn" data-product='{"name": "Boho Chic Maxi Dress", "price": "₹3,800", "description": "A flowing maxi dress with intricate patterns, perfect for a bohemian look.", "image": "https://placehold.co/600x400/E1C1FF/333333?text=Boho+Maxi+Dress"}'>Quick View</button>
                        </div>
                    </div>
                </div>

                <!-- New Product Card 2 -->
                <div class="product-card bg-gray-50 rounded-xl shadow-lg overflow-hidden transform hover:-translate-y-2 transition-all duration-300 ease-in-out" data-aos="fade-up" data-aos-delay="200">
                    <img src="https://placehold.co/600x400/FFE1C1/333333?text=Stylish+Blazer" alt="Stylish Blazer" class="w-full h-64 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Contemporary Office Blazer</h3>
                        <p class="text-gray-600 mb-4">Sharp and sophisticated for your professional look.</p>
                        <div class="flex justify-between items-center">
                            <span class="text-2xl font-bold text-indigo-600">₹5,200</span>
                            <button class="bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors duration-200 text-sm font-medium quick-view-btn" data-product='{"name": "Contemporary Office Blazer", "price": "₹5,200", "description": "A sleek and modern blazer that elevates your office attire with a touch of elegance.", "image": "https://placehold.co/600x400/FFE1C1/333333?text=Stylish+Blazer"}'>Quick View</button>
                        </div>
                    </div>
                </div>

                <!-- New Product Card 3 -->
                <div class="product-card bg-gray-50 rounded-xl shadow-lg overflow-hidden transform hover:-translate-y-2 transition-all duration-300 ease-in-out" data-aos="fade-up" data-aos-delay="300">
                    <img src="https://placehold.co/600x400/C1FFE1/333333?text=Graphic+T-Shirt" alt="Graphic T-Shirt" class="w-full h-64 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Urban Graphic T-Shirt</h3>
                        <p class="text-gray-600 mb-4">Express yourself with unique designs.</p>
                        <div class="flex justify-between items-center">
                            <span class="text-2xl font-bold text-indigo-600">₹950</span>
                            <button class="bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors duration-200 text-sm font-medium quick-view-btn" data-product='{"name": "Urban Graphic T-Shirt", "price": "₹950", "description": "Comfortable cotton t-shirt with a bold graphic print, perfect for casual wear.", "image": "https://placehold.co/600x400/C1FFE1/333333?text=Graphic+T-Shirt"}'>Quick View</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Shop by Category Section -->
    <section id="categories" class="py-16 md:py-24 bg-gray-100">
        <div class="container mx-auto px-6 md:px-12">
            <h2 class="text-4xl md:text-5xl font-extrabold text-center text-gray-900 mb-12" data-aos="fade-up">Shop by Category</h2>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Category Card 1 -->
                <div class="category-card bg-white rounded-xl shadow-lg overflow-hidden transform hover:-translate-y-2 transition-all duration-300 ease-in-out cursor-pointer" data-aos="fade-up" data-aos-delay="100">
                    <img src="https://placehold.co/400x300/FFD1D1/333333?text=Women" alt="Women's Fashion" class="w-full h-48 object-cover">
                    <div class="p-4 text-center">
                        <h3 class="text-xl font-semibold text-gray-900">Women's Fashion</h3>
                    </div>
                </div>
                <!-- Category Card 2 -->
                <div class="category-card bg-white rounded-xl shadow-lg overflow-hidden transform hover:-translate-y-2 transition-all duration-300 ease-in-out cursor-pointer" data-aos="fade-up" data-aos-delay="200">
                    <img src="https://placehold.co/400x300/D1FFD1/333333?text=Men" alt="Men's Fashion" class="w-full h-48 object-cover">
                    <div class="p-4 text-center">
                        <h3 class="text-xl font-semibold text-gray-900">Men's Fashion</h3>
                    </div>
                </div>
                <!-- Category Card 3 -->
                <div class="category-card bg-white rounded-xl shadow-lg overflow-hidden transform hover:-translate-y-2 transition-all duration-300 ease-in-out cursor-pointer" data-aos="fade-up" data-aos-delay="300">
                    <img src="https://placehold.co/400x300/D1D1FF/333333?text=Accessories" alt="Accessories" class="w-full h-48 object-cover">
                    <div class="p-4 text-center">
                        <h3 class="text-xl font-semibold text-gray-900">Accessories</h3>
                    </div>
                </div>
                <!-- Category Card 4 -->
                <div class="category-card bg-white rounded-xl shadow-lg overflow-hidden transform hover:-translate-y-2 transition-all duration-300 ease-in-out cursor-pointer" data-aos="fade-up" data-aos-delay="400">
                    <img src="https://placehold.co/400x300/FFDDC1/333333?text=Footwear" alt="Footwear" class="w-full h-48 object-cover">
                    <div class="p-4 text-center">
                        <h3 class="text-xl font-semibold text-gray-900">Footwear</h3>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick View Modal Structure -->
    <div id="quick-view-modal" class="fixed inset-0 hidden modal-overlay">
        <div class="bg-white rounded-xl shadow-2xl p-8 w-11/12 md:w-3/4 lg:w-1/2 relative modal-content">
            <button id="close-modal-btn" class="absolute top-4 right-4 text-gray-500 hover:text-gray-800 text-2xl">
                <i class="fas fa-times"></i>
            </button>
            <div class="flex flex-col md:flex-row gap-8">
                <div class="md:w-1/2">
                    <img id="modal-product-image" src="" alt="Product Image" class="w-full h-72 object-cover rounded-lg">
                </div>
                <div class="md:w-1/2">
                    <h3 id="modal-product-name" class="text-3xl font-bold text-gray-900 mb-4"></h3>
                    <p id="modal-product-description" class="text-gray-700 text-lg mb-6"></p>
                    <p id="modal-product-price" class="text-indigo-600 text-4xl font-extrabold mb-6"></p>
                    <button class="bg-indigo-600 text-white py-3 px-6 rounded-lg text-lg font-semibold hover:bg-indigo-700 transition-colors duration-200 w-full md:w-auto">Add to Cart</button>
                </div>
            </div>
        </div>
    </div>


    <!-- About Section -->
    <section id="about" class="py-16 md:py-24 bg-white">
        <div class="container mx-auto px-6 md:px-12 flex flex-col md:flex-row items-center gap-12">
            <div class="md:w-1/2" data-aos="fade-right">
                <img src="https://placehold.co/800x600/C1E0FF/333333?text=About+Us" alt="About Us" class="rounded-xl shadow-lg w-full h-auto object-cover">
            </div>
            <div class="md:w-1/2" data-aos="fade-left">
                <h2 class="text-4xl md:text-5xl font-extrabold text-gray-900 mb-6">About Fashion Hub</h2>
                <p class="text-lg text-gray-700 mb-4 leading-relaxed">
                    At Fashion Hub, we believe that fashion is more than just clothing; it's a form of self-expression. We are dedicated to bringing you the latest trends and timeless pieces that empower you to showcase your unique personality and style.
                </p>
                <p class="text-lg text-gray-700 leading-relaxed">
                    Our curated collections are handpicked for quality, comfort, and unparalleled style. From casual wear to elegant evening attire, we have something for every occasion. Join our community and discover a world where fashion meets individuality.
                </p>
            </div>
        </div>
    </section>

    <!-- Our Values Section -->
    <section id="values" class="py-16 md:py-24 bg-gray-100">
        <div class="container mx-auto px-6 md:px-12">
            <h2 class="text-4xl md:text-5xl font-extrabold text-center text-gray-900 mb-12" data-aos="fade-up">Our Values</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 text-center">
                <div class="bg-white p-8 rounded-xl shadow-lg flex flex-col items-center" data-aos="fade-up" data-aos-delay="100">
                    <div class="text-indigo-600 text-5xl mb-4"><i class="fas fa-gem"></i></div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">Quality Craftsmanship</h3>
                    <p class="text-gray-700">We meticulously select materials and ensure superior craftsmanship in every product.</p>
                </div>
                <div class="bg-white p-8 rounded-xl shadow-lg flex flex-col items-center" data-aos="fade-up" data-aos-delay="200">
                    <div class="text-indigo-600 text-5xl mb-4"><i class="fas fa-leaf"></i></div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">Sustainable Fashion</h3>
                    <p class="text-gray-700">Committed to ethical sourcing and environmentally friendly practices.</p>
                </div>
                <div class="bg-white p-8 rounded-xl shadow-lg flex flex-col items-center" data-aos="fade-up" data-aos-delay="300">
                    <div class="text-indigo-600 text-5xl mb-4"><i class="fas fa-palette"></i></div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">Unique Style</h3>
                    <p class="text-gray-700">Curating distinctive pieces that allow your individuality to shine.</p>
                </div>
                <div class="bg-white p-8 rounded-xl shadow-lg flex flex-col items-center" data-aos="fade-up" data-aos-delay="400">
                    <div class="text-indigo-600 text-5xl mb-4"><i class="fas fa-heart"></i></div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">Customer Satisfaction</h3>
                    <p class="text-gray-700">Your happiness is our priority, with dedicated support and service.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="py-16 md:py-24 bg-white">
        <div class="container mx-auto px-6 md:px-12">
            <h2 class="text-4xl md:text-5xl font-extrabold text-center text-gray-900 mb-12" data-aos="fade-up">What Our Customers Say</h2>

            <div class="relative w-full max-w-4xl mx-auto overflow-hidden rounded-xl shadow-lg bg-gray-100 p-8">
                <div id="testimonial-slider" class="flex transition-transform duration-500 ease-in-out">
                    <!-- Testimonial 1 -->
                    <div class="w-full flex-shrink-0 text-center p-6">
                        <img src="https://placehold.co/100x100/F0F0F0/888888?text=JS" alt="Customer Avatar" class="w-24 h-24 rounded-full mx-auto mb-6 object-cover border-4 border-indigo-200 shadow-md">
                        <p class="text-2xl italic text-gray-700 mb-6">"The quality of the clothing from Fashion Hub is exceptional! Every piece feels luxurious and fits perfectly. I always receive compliments when I wear their designs."</p>
                        <p class="text-lg font-semibold text-indigo-600">- Jane Smith</p>
                        <p class="text-gray-500">Fashion Enthusiast</p>
                    </div>
                    <!-- Testimonial 2 -->
                    <div class="w-full flex-shrink-0 text-center p-6">
                        <img src="https://placehold.co/100x100/E0E0E0/777777?text=MB" alt="Customer Avatar" class="w-24 h-24 rounded-full mx-auto mb-6 object-cover border-4 border-indigo-200 shadow-md">
                        <p class="text-2xl italic text-gray-700 mb-6">"I love the unique selection they offer. It's so refreshing to find stylish clothes that stand out from the crowd. Their customer service is also top-notch!"</p>
                        <p class="text-lg font-semibold text-indigo-600">- Michael Brown</p>
                        <p class="text-gray-500">Trendsetter</p>
                    </div>
                    <!-- Testimonial 3 -->
                    <div class="w-full flex-shrink-0 text-center p-6">
                        <img src="https://placehold.co/100x100/D0D0D0/666666?text=AL" alt="Customer Avatar" class="w-24 h-24 rounded-full mx-auto mb-6 object-cover border-4 border-indigo-200 shadow-md">
                        <p class="text-2xl italic text-gray-700 mb-6">"Fast shipping and incredible quality! I'm always impressed with my purchases. Fashion Hub is my go-to for all my fashion needs."</p>
                        <p class="text-lg font-semibold text-indigo-600">- Anna Lee</p>
                        <p class="text-gray-500">Happy Shopper</p>
                    </div>
                </div>

                <!-- Slider Navigation Dots -->
                <div id="slider-dots" class="flex justify-center mt-8 space-x-2 slider-dots">
                    <button class="w-3 h-3 rounded-full bg-gray-300 focus:outline-none active" data-slide-to="0"></button>
                    <button class="w-3 h-3 rounded-full bg-gray-300 focus:outline-none" data-slide-to="1"></button>
                    <button class="w-3 h-3 rounded-full bg-gray-300 focus:outline-none" data-slide-to="2"></button>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Signup Section -->
    <section id="contact" class="py-16 md:py-24 bg-gradient-to-r from-indigo-500 to-purple-600 text-white text-center">
        <div class="container mx-auto px-6 md:px-12" data-aos="zoom-in">
            <h2 class="text-4xl md:text-5xl font-extrabold mb-6">Stay Updated with Fashion Hub</h2>
            <p class="text-xl md:text-2xl mb-8 max-w-2xl mx-auto">Subscribe to our newsletter for exclusive offers, new arrivals, and style tips directly to your inbox.</p>
            <form id="newsletter-form" class="max-w-xl mx-auto flex flex-col sm:flex-row gap-4">
                <input type="email" id="email-input" placeholder="Enter your email address" class="newsletter-input flex-grow p-4 rounded-lg border-2 border-transparent focus:ring-4 focus:ring-indigo-300 focus:border-indigo-400 transition-all duration-200 text-gray-900 placeholder-gray-500" required>
                <button type="submit" class="bg-white text-indigo-700 font-bold py-4 px-8 rounded-lg hover:bg-indigo-100 transition-colors duration-200 shadow-md">Subscribe</button>
            </form>
            <div id="newsletter-message" class="mt-4 text-lg font-medium"></div>
        </div>
    </section>

    <!-- Footer Section -->
    <footer class="bg-gray-900 text-gray-300 py-12">
        <div class="container mx-auto px-6 md:px-12 grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- About Column -->
            <div class="mb-6 md:mb-0">
                <h3 class="text-xl font-bold text-white mb-4">Fashion Hub</h3>
                <p class="text-sm leading-relaxed">Your ultimate destination for trendy and high-quality fashion. We empower you to express your unique style with confidence.</p>
            </div>

            <!-- Quick Links Column -->
            <div class="mb-6 md:mb-0">
                <h3 class="text-xl font-bold text-white mb-4">Quick Links</h3>
                <ul>
                    <li class="mb-2"><a href="#shop" class="text-gray-400 hover:text-white transition-colors duration-200">Shop</a></li>
                    <li class="mb-2"><a href="#new-arrivals" class="text-gray-400 hover:text-white transition-colors duration-200">New Arrivals</a></li>
                    <li class="mb-2"><a href="#categories" class="text-gray-400 hover:text-white transition-colors duration-200">Categories</a></li>
                    <li class="mb-2"><a href="#about" class="text-gray-400 hover:text-white transition-colors duration-200">About Us</a></li>
                    <li class="mb-2"><a href="#contact" class="text-gray-400 hover:text-white transition-colors duration-200">Contact</a></li>
                    <li class="mb-2"><a href="#" class="text-gray-400 hover:text-white transition-colors duration-200">Privacy Policy</a></li>
                </ul>
            </div>

            <!-- Social Media Column -->
            <div>
                <h3 class="text-xl font-bold text-white mb-4">Connect With Us</h3>
                <div class="flex space-x-6">
                    <a href="#" class="text-gray-400 hover:text-white transform hover:scale-110 transition-transform duration-200 text-2xl"><i class="fab fa-facebook-f"></i></a>
                    <a href="#" class="text-gray-400 hover:text-white transform hover:scale-110 transition-transform duration-200 text-2xl"><i class="fab fa-twitter"></i></a>
                    <a href="#" class="text-gray-400 hover:text-white transform hover:scale-110 transition-transform duration-200 text-2xl"><i class="fab fa-instagram"></i></a>
                    <a href="#" class="text-gray-400 hover:text-white transform hover:scale-110 transition-transform duration-200 text-2xl"><i class="fab fa-linkedin-in"></i></a>
                </div>
            </div>
        </div>
        <div class="text-center text-gray-500 text-sm mt-8 border-t border-gray-700 pt-8">
            &copy; 2024 Fashion Hub. All rights reserved.
        </div>
    </footer>

    <!-- AOS JavaScript -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true, // Only animate once
        });

        // Sticky Header Logic
        const header = document.getElementById('main-header');
        const heroSection = document.getElementById('hero');
        const heroHeight = heroSection.offsetHeight; // Get height of hero section

        window.addEventListener('scroll', () => {
            if (window.scrollY > heroHeight - header.offsetHeight) { // Adjust threshold
                header.classList.add('header-sticky');
            } else {
                header.classList.remove('header-sticky');
            }
        });

        // Mobile Menu Toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');

        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });

        // Close mobile menu when a link is clicked
        function closeMobileMenu() {
            mobileMenu.classList.add('hidden');
        }

        // Quick View Modal Logic
        const quickViewButtons = document.querySelectorAll('.quick-view-btn');
        const quickViewModal = document.getElementById('quick-view-modal');
        const closeModalBtn = document.getElementById('close-modal-btn');
        const modalProductName = document.getElementById('modal-product-name');
        const modalProductPrice = document.getElementById('modal-product-price');
        const modalProductDescription = document.getElementById('modal-product-description');
        const modalProductImage = document.getElementById('modal-product-image');

        quickViewButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Ensure the product data correctly reflects INR
                const productData = JSON.parse(button.dataset.product);
                modalProductName.textContent = productData.name;
                modalProductPrice.textContent = productData.price; // This will now display the INR value
                modalProductDescription.textContent = productData.description;
                modalProductImage.src = productData.image;
                modalProductImage.alt = productData.name; // Set alt text for accessibility
                quickViewModal.classList.remove('hidden');
            });
        });

        closeModalBtn.addEventListener('click', () => {
            quickViewModal.classList.add('hidden');
        });

        // Close modal if clicked outside of content
        quickViewModal.addEventListener('click', (event) => {
            if (event.target === quickViewModal) {
                quickViewModal.classList.add('hidden');
            }
        });

        // Testimonials Slider Logic
        const slider = document.getElementById('testimonial-slider');
        const dotsContainer = document.getElementById('slider-dots');
        const testimonials = slider.children;
        let currentSlide = 0;
        const totalSlides = testimonials.length;

        function updateSlider() {
            slider.style.transform = `translateX(${-currentSlide * 100}%)`;
            updateDots();
        }

        function updateDots() {
            Array.from(dotsContainer.children).forEach((dot, index) => {
                if (index === currentSlide) {
                    dot.classList.add('active');
                    dot.classList.add('bg-indigo-600');
                } else {
                    dot.classList.remove('active');
                    dot.classList.remove('bg-indigo-600');
                }
            });
        }

        dotsContainer.addEventListener('click', (event) => {
            if (event.target.tagName === 'BUTTON') {
                const slideTo = parseInt(event.target.dataset.slideTo);
                currentSlide = slideTo;
                updateSlider();
            }
        });

        // Auto-play for slider
        let slideInterval = setInterval(() => {
            currentSlide = (currentSlide + 1) % totalSlides;
            updateSlider();
        }, 5000); // Change slide every 5 seconds

        // Pause auto-play on hover
        slider.addEventListener('mouseenter', () => clearInterval(slideInterval));
        slider.addEventListener('mouseleave', () => {
            slideInterval = setInterval(() => {
                currentSlide = (currentSlide + 1) % totalSlides;
                updateSlider();
            });
        });

        // Initial slider setup
        updateSlider();


        // Newsletter Signup Form Validation
        const newsletterForm = document.getElementById('newsletter-form');
        const emailInput = document.getElementById('email-input');
        const newsletterMessage = document.getElementById('newsletter-message');

        newsletterForm.addEventListener('submit', (event) => {
            event.preventDefault(); // Prevent default form submission

            const email = emailInput.value;
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/; // Basic email regex

            if (emailRegex.test(email)) {
                newsletterMessage.textContent = 'Thank you for subscribing!';
                newsletterMessage.classList.remove('text-red-500');
                newsletterMessage.classList.add('text-green-300');
                emailInput.value = ''; // Clear input
            } else {
                newsletterMessage.textContent = 'Please enter a valid email address.';
                newsletterMessage.classList.remove('text-green-300');
                newsletterMessage.classList.add('text-red-300');
            }
        });
    </script>
</body>
</html>
