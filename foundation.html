<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SaaSnext Foundation - Empowering Communities</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- AOS CSS -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- Google Font: Inter -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #F8F4E3; /* Warm background */
        }
        .hero-section {
            /* Updated stock image for hero section */
            background-image: url('https://images.pexels.com/photos/6646918/pexels-photo-6646918.jpeg');
            background-size: cover;
            background-position: center;
            position: relative;
            z-index: 1;
        }
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5); /* Dark overlay */
            z-index: -1;
        }
        /* Custom spiritual color palette */
        .bg-spiritual-green { background-color: #4CAF50; } /* A warm green */
        .text-spiritual-green { color: #4CAF50; }
        .bg-spiritual-gold { background-color: #FFC107; } /* A warm gold/yellow */
        .text-spiritual-gold { color: #FFC107; }
        .bg-spiritual-sand { background-color: #F8F4E3; } /* Light, earthy background */
        .text-spiritual-sand { color: #F8F4E3; }
        .bg-spiritual-clay { background-color: #A0522D; } /* Earthy brown */
        .text-spiritual-clay { color: #A0522D; }
        .bg-spiritual-blue { background-color: #6495ED; } /* Muted blue */
        .text-spiritual-blue { color: #6495ED; }

        /* General button styling */
        .btn-primary {
            @apply bg-spiritual-green text-white px-8 py-3 rounded-full font-semibold hover:bg-green-700 transition duration-300 shadow-lg;
        }
    </style>
</head>
<body>

    <!-- Navbar -->
    <nav class="bg-white p-4 shadow-md fixed w-full z-50">
        <div class="container mx-auto flex justify-between items-center">
            <!-- Logo/Site Title -->
            <a href="#" class="text-2xl font-bold text-spiritual-clay">SaaSnext Foundation</a>

            <!-- Mobile Menu Button -->
            <div class="block lg:hidden">
                <button id="nav-toggle" class="text-spiritual-clay focus:outline-none focus:text-spiritual-green">
                    <i class="fas fa-bars text-2xl"></i>
                </button>
            </div>

            <!-- Desktop Menu -->
            <div class="hidden lg:flex items-center space-x-6" id="nav-content-desktop">
                <a href="#our-mission" class="text-gray-700 hover:text-spiritual-green font-medium transition duration-300">Our Mission</a>
                <a href="#projects" class="text-gray-700 hover:text-spiritual-green font-medium transition duration-300">Projects</a>
                <a href="#testimonials" class="text-gray-700 hover:text-spiritual-green font-medium transition duration-300">Testimonials</a>
                <a href="#donate" class="btn-primary">Donate</a>
            </div>
        </div>

        <!-- Mobile Menu (hidden by default) -->
        <div class="hidden lg:hidden mt-4" id="nav-content-mobile">
            <a href="#our-mission" class="block py-2 px-4 text-gray-700 hover:bg-gray-100 rounded-md transition duration-300">Our Mission</a>
            <a href="#projects" class="block py-2 px-4 text-gray-700 hover:bg-gray-100 rounded-md transition duration-300">Projects</a>
            <a href="#testimonials" class="block py-2 px-4 text-gray-700 hover:bg-gray-100 rounded-md transition duration-300">Testimonials</a>
            <a href="#donate" class="block mt-2 text-center btn-primary">Donate</a>
        </div>
    </nav>

    <!-- Spacer to push content below fixed navbar -->
    <div class="pt-20 lg:pt-24"></div>

    <!-- Hero Section -->
    <section class="hero-section text-white flex items-center justify-center min-h-screen text-center py-20 px-4" data-aos="fade-in" data-aos-duration="1500">
        <div class="max-w-4xl mx-auto z-10">
            <h1 class="text-4xl sm:text-5xl md:text-6xl font-bold mb-6 leading-tight" data-aos="fade-up" data-aos-delay="300">
                Building Brighter Futures, One Community at a Time
            </h1>
            <p class="text-lg sm:text-xl md:text-2xl mb-10" data-aos="fade-up" data-aos-delay="500">
                Empowering the underserved through education, healthcare, and sustainable living.
            </p>
            <a href="#donate" class="btn-primary" data-aos="zoom-in" data-aos-delay="700">
                Donate Now
            </a>
        </div>
    </section>

    <!-- Our Mission Section -->
    <section id="our-mission" class="py-16 md:py-24 bg-spiritual-sand text-gray-800 px-4">
        <div class="max-w-screen-xl mx-auto">
            <h2 class="text-3xl sm:text-4xl font-bold text-center mb-12 text-spiritual-clay" data-aos="fade-up">Our Mission</h2>
            <div class="grid md:grid-cols-2 gap-12 items-center">
                <div data-aos="fade-right" data-aos-delay="200">
                    <p class="text-lg leading-relaxed mb-6">
                        At SaaSnext Foundation, we believe in the transformative power of compassion and collective action. Our mission is to uplift marginalized communities by providing access to essential resources and opportunities that foster self-sufficiency and holistic well-being.
                    </p>
                    <p class="text-lg leading-relaxed mb-6">
                        We focus on sustainable initiatives in education, healthcare, environmental conservation, and economic empowerment, ensuring long-term positive impact and empowering individuals to build a better tomorrow for themselves and their families.
                    </p>
                </div>
                <div class="space-y-6" data-aos="fade-left" data-aos-delay="400">
                    <div class="flex items-start">
                        <i class="fas fa-hand-holding-heart text-spiritual-green text-3xl mr-4 flex-shrink-0"></i>
                        <div>
                            <h3 class="font-semibold text-xl mb-1 text-spiritual-blue">Compassionate Outreach</h3>
                            <p class="text-gray-700">Reaching out to those in greatest need with empathy and understanding.</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <i class="fas fa-lightbulb text-spiritual-green text-3xl mr-4 flex-shrink-0"></i>
                        <div>
                            <h3 class="font-semibold text-xl mb-1 text-spiritual-blue">Sustainable Solutions</h3>
                            <p class="text-gray-700">Implementing programs that create lasting positive change.</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <i class="fas fa-users text-spiritual-green text-3xl mr-4 flex-shrink-0"></i>
                        <div>
                            <h3 class="font-semibold text-xl mb-1 text-spiritual-blue">Community Empowerment</h3>
                            <p class="text-gray-700">Equipping individuals with tools for self-reliance and growth.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Impact Counters Section -->
    <section class="py-16 bg-spiritual-gold text-white px-4">
        <div class="max-w-screen-xl mx-auto text-center">
            <h2 class="text-3xl sm:text-4xl font-bold mb-12 text-spiritual-clay" data-aos="fade-up">Our Impact So Far</h2>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-8">
                <div class="p-6 rounded-lg bg-spiritual-green shadow-xl" data-aos="zoom-in" data-aos-delay="100">
                    <i class="fas fa-bowl-food text-5xl mb-4"></i>
                    <div class="text-5xl font-bold mb-2 counter" data-target="150000">0</div>
                    <p class="text-xl">Meals Served</p>
                </div>
                <div class="p-6 rounded-lg bg-spiritual-green shadow-xl" data-aos="zoom-in" data-aos-delay="300">
                    <i class="fas fa-handshake-angle text-5xl mb-4"></i>
                    <div class="text-5xl font-bold mb-2 counter" data-target="5000">0</div>
                    <p class="text-xl">Dedicated Volunteers</p>
                </div>
                <div class="p-6 rounded-lg bg-spiritual-green shadow-xl" data-aos="zoom-in" data-aos-delay="500">
                    <i class="fas fa-map-marker-alt text-5xl mb-4"></i>
                    <div class="text-5xl font-bold mb-2 counter" data-target="75">0</div>
                    <p class="text-xl">Locations Impacted</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects/Impact Section -->
    <section id="projects" class="py-16 md:py-24 bg-spiritual-sand text-gray-800 px-4">
        <div class="max-w-screen-xl mx-auto">
            <h2 class="text-3xl sm:text-4xl font-bold text-center mb-12 text-spiritual-clay" data-aos="fade-up">Our Projects & Impact</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Project Card 1 -->
                <div class="bg-white rounded-lg shadow-lg overflow-hidden" data-aos="fade-up" data-aos-delay="100">
                    <img src="https://images.unsplash.com/photo-1522071820081-009f0129c76d?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Education Project" class="w-full h-56 object-cover">
                    <div class="p-6">
                        <h3 class="font-bold text-xl mb-3 text-spiritual-blue">Education for All</h3>
                        <p class="text-gray-700 leading-relaxed">
                            Providing quality education to underprivileged children through school supplies, scholarships, and after-school programs.
                        </p>
                    </div>
                </div>
                <!-- Project Card 2 -->
                <div class="bg-white rounded-lg shadow-lg overflow-hidden" data-aos="fade-up" data-aos-delay="300">
                    <img src="https://images.unsplash.com/photo-1532938911079-cdff2bd99195?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Healthcare Project" class="w-full h-56 object-cover">
                    <div class="p-6">
                        <h3 class="font-bold text-xl mb-3 text-spiritual-blue">Community Health Initiatives</h3>
                        <p class="text-gray-700 leading-relaxed">
                            Organizing free medical camps, health awareness campaigns, and providing access to basic healthcare services.
                        </p>
                    </div>
                </div>
                <!-- Project Card 3 -->
                <div class="bg-white rounded-lg shadow-lg overflow-hidden" data-aos="fade-up" data-aos-delay="500">
                    <img src="https://images.unsplash.com/photo-1532463768652-cf8535492d6e?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Environment Project" class="w-full h-56 object-cover">
                    <div class="p-6">
                        <h3 class="font-bold text-xl mb-3 text-spiritual-blue">Environmental Sustainability</h3>
                        <p class="text-gray-700 leading-relaxed">
                            Promoting eco-friendly practices, tree plantation drives, and waste management programs in rural areas.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="py-16 md:py-24 bg-spiritual-blue text-white px-4">
        <div class="max-w-screen-xl mx-auto text-center">
            <h2 class="text-3xl sm:text-4xl font-bold mb-12 text-white" data-aos="fade-up">Stories of Transformation</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-10">
                <!-- Testimonial Card 1 -->
                <div class="bg-white text-gray-800 p-8 rounded-lg shadow-lg text-left" data-aos="fade-right" data-aos-delay="100">
                    <div class="flex items-center mb-6">
                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Beneficiary Jane Doe" class="w-20 h-20 rounded-full object-cover border-4 border-spiritual-green mr-4">
                        <div>
                            <p class="font-semibold text-lg">Jane Doe</p>
                            <p class="text-sm text-gray-600">Scholarship Recipient</p>
                        </div>
                    </div>
                    <p class="italic leading-relaxed">
                        "Thanks to SaaSnext Foundation, I received a scholarship that changed my life. I'm now pursuing my dream of becoming a teacher. Their support made all the difference."
                    </p>
                </div>
                <!-- Testimonial Card 2 -->
                <div class="bg-white text-gray-800 p-8 rounded-lg shadow-lg text-left" data-aos="fade-left" data-aos-delay="300">
                    <div class="flex items-center mb-6">
                        <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=2000&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D" alt="Beneficiary John Smith" class="w-20 h-20 rounded-full object-cover border-4 border-spiritual-green mr-4">
                        <div>
                            <p class="font-semibold text-lg">John Smith</p>
                            <p class="text-sm text-gray-600">Community Health Beneficiary</p>
                        </div>
                    </div>
                    <p class="italic leading-relaxed">
                        "The free medical camp organized by SaaSnext Foundation helped me get the timely check-up I needed. Their dedication to community health is truly inspiring."
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Donate Section -->
    <section id="donate" class="py-16 md:py-24 bg-spiritual-sand text-gray-800 px-4">
        <div class="max-w-screen-xl mx-auto">
            <h2 class="text-3xl sm:text-4xl font-bold text-center mb-12 text-spiritual-clay" data-aos="fade-up">Support Our Cause</h2>
            <div class="grid md:grid-cols-2 gap-12 items-center">
                <div class="p-8 bg-white rounded-lg shadow-lg" data-aos="fade-right" data-aos-delay="100">
                    <h3 class="font-bold text-2xl mb-6 text-spiritual-blue">Make a Difference Today</h3>
                    <form>
                        <div class="mb-4">
                            <label for="amount" class="block text-gray-700 text-sm font-bold mb-2">Donation Amount ($)</label>
                            <input type="number" id="amount" name="amount" min="1" value="50"
                                class="shadow appearance-none border rounded-lg w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus:shadow-outline focus:border-spiritual-green"
                                placeholder="e.g., 50">
                        </div>
                        <div class="mb-4">
                            <label for="name" class="block text-gray-700 text-sm font-bold mb-2">Your Name</label>
                            <input type="text" id="name" name="name"
                                class="shadow appearance-none border rounded-lg w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus:shadow-outline focus:border-spiritual-green"
                                placeholder="John Doe">
                        </div>
                        <div class="mb-6">
                            <label for="email" class="block text-gray-700 text-sm font-bold mb-2">Your Email</label>
                            <input type="email" id="email" name="email"
                                class="shadow appearance-none border rounded-lg w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus:shadow-outline focus:border-spiritual-green"
                                placeholder="<EMAIL>">
                        </div>
                        <button type="submit" class="btn-primary w-full">Donate Now</button>
                    </form>
                    <p class="text-center text-sm text-gray-600 mt-4">
                        Your generous contribution is tax-deductible.
                    </p>
                </div>
                <div class="text-center" data-aos="fade-left" data-aos-delay="300">
                    <h3 class="font-bold text-2xl mb-4 text-spiritual-blue">Scan to Donate</h3>
                    <img src="https://api.qrserver.com/v1/create-qr-code/?size=250x250&data=https://saasnextfoundation.org/donate" alt="QR Code for Donation" class="mx-auto rounded-lg shadow-lg border-4 border-spiritual-green">
                    <p class="text-gray-700 mt-4 text-lg">Easily donate via your preferred payment app.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-10 px-4">
        <div class="max-w-screen-xl mx-auto text-center">
            <p class="mb-4">&copy; 2025 SaaSnext Foundation. All rights reserved.</p>
            <div class="flex justify-center space-x-6">
                <a href="#" class="text-gray-400 hover:text-white transition duration-300"><i class="fab fa-facebook-f text-xl"></i></a>
                <a href="#" class="text-gray-400 hover:text-white transition duration-300"><i class="fab fa-twitter text-xl"></i></a>
                <a href="#" class="text-gray-400 hover:text-white transition duration-300"><i class="fab fa-instagram text-xl"></i></a>
                <a href="#" class="text-gray-400 hover:text-white transition duration-300"><i class="fab fa-linkedin-in text-xl"></i></a>
            </div>
            <p class="text-sm text-gray-500 mt-4">Made with <i class="fas fa-heart text-red-500"></i> for a better world.</p>
        </div>
    </footer>

    <!-- AOS JS -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true, // Whether animation should happen only once - while scrolling down
        });

        // Counter Animation Script
        const counters = document.querySelectorAll('.counter');
        const speed = 200; // The lower the speed, the faster the counter

        const animateCounter = (counter) => {
            const target = +counter.getAttribute('data-target');
            let count = 0;

            const updateCount = () => {
                const increment = target / speed;
                if (count < target) {
                    count = Math.ceil(count + increment);
                    counter.innerText = count.toLocaleString(); // Add comma separator
                    setTimeout(updateCount, 1);
                } else {
                    counter.innerText = target.toLocaleString();
                }
            };

            // Intersection Observer to start animation when element is visible
            const observer = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        updateCount();
                        observer.unobserve(entry.target); // Stop observing once animated
                    }
                });
            }, { threshold: 0.5 }); // Trigger when 50% of the element is visible

            observer.observe(counter);
        };

        counters.forEach(counter => animateCounter(counter));

        // Smooth scroll for internal links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();

                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Navbar Toggle Script for Mobile
        const navToggle = document.getElementById('nav-toggle');
        const navContentMobile = document.getElementById('nav-content-mobile');

        navToggle.addEventListener('click', () => {
            navContentMobile.classList.toggle('hidden');
        });
    </script>
</body>
</html>
