<!DOCTYPE html>
<html lang="en" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SaaSnext Academy - Build Your Career</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Custom CSS for typing effect cursor and general styling */
        body {
            font-family: 'Inter', sans-serif;
            transition: background-color 0.3s, color 0.3s;
        }
        .typing-effect::after {
            content: '|';
            display: inline-block;
            animation: blink-caret 0.75s infinite;
        }
        @keyframes blink-caret {
            from, to { border-color: transparent; }
            50% { border-color: currentColor; }
        }

        /* Dark mode specific styles */
        .dark {
            background-color: #1a202c; /* Dark background */
            color: #e2e8f0; /* Light text */
        }
        .dark .bg-white {
            background-color: #2d3748; /* Darker background for cards */
        }
        .dark .text-gray-800 {
            color: #e2e8f0;
        }
        .dark .text-gray-600 {
            color: #cbd5e0;
        }
        .dark .border-gray-200 {
            border-color: #4a5568;
        }
        .dark .hover\:bg-blue-50 {
            background-color: #4a5568;
        }
        .dark input, .dark textarea {
            background-color: #2d3748;
            border-color: #4a5568;
            color: #e2e8f0;
        }
        .dark input::placeholder, .dark textarea::placeholder {
            color: #a0aec0;
        }

        /* Custom scrollbar for better aesthetics */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        .dark::-webkit-scrollbar-track {
            background: #2d3748;
        }
        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 10px;
        }
        .dark::-webkit-scrollbar-thumb {
            background: #6b7280;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        .dark::-webkit-scrollbar-thumb:hover {
            background: #50555d;
        }

        /* Chart animation */
        .chart-bar {
            width: 0; /* Start at 0 width */
            transition: width 1s ease-out; /* Animate width */
        }
        .chart-bar.animate {
            width: var(--bar-width); /* Animate to actual width */
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800 transition-colors duration-300">

    <!-- Dark/Light Mode Toggle Button -->
    <button id="themeToggle" class="fixed top-4 right-4 z-50 p-3 rounded-full bg-blue-600 text-white shadow-lg hover:bg-blue-700 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-moon hidden dark:block"><path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path></svg>
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-sun dark:hidden"><circle cx="12" cy="12" r="5"></circle><line x1="12" y1="1" x2="12" y2="3"></line><line x1="12" y1="21" x2="12" y2="23"></line><line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line><line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line><line x1="1" y1="12" x2="3" y2="12"></line><line x1="21" y1="12" x2="23" y2="12"></line><line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line><line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line></svg>
    </button>

    <!-- Header Section -->
    <header class="bg-white dark:bg-gray-900 text-gray-800 dark:text-white shadow-md p-4 rounded-b-xl transition-colors duration-300 sticky top-0 z-40">
        <div class="container mx-auto flex justify-between items-center">
            <a href="#" class="text-2xl font-bold text-blue-600 dark:text-blue-400">SaaSnext Academy</a>
            <nav>
                <ul class="flex space-x-6">
                    <li><a href="#hero" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">Home</a></li>
                    <li><a href="#courses" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">Courses</a></li>
                    <li><a href="#why-choose-us" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">Why Us</a></li>
                    <li><a href="#faculty" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">Faculty</a></li>
                    <li><a href="#lead-form" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">Contact</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="hero" class="relative min-h-screen flex items-center justify-center bg-gradient-to-r from-blue-600 to-indigo-700 text-white p-6 rounded-b-3xl shadow-xl">
        <div class="text-center z-10">
            <h1 class="text-5xl md:text-7xl font-extrabold mb-4 animate-fade-in-down">SaaSnext Academy</h1>
            <p class="text-2xl md:text-4xl font-light typing-effect" id="typing-text">We Build Careers</p>
            <a href="#lead-form" class="mt-8 inline-block bg-white text-blue-600 font-bold py-3 px-8 rounded-full shadow-lg hover:bg-gray-100 transform hover:scale-105 transition-all duration-300">
                Enroll Now!
            </a>
        </div>
    </section>

    <!-- Courses Section -->
    <section id="courses" class="py-16 bg-white dark:bg-gray-800 transition-colors duration-300 rounded-3xl shadow-lg m-6 mt-[-3rem]">
        <div class="container mx-auto px-6">
            <h2 class="text-4xl font-bold text-center mb-12 text-gray-800 dark:text-white">Our Courses</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Course Card 1 -->
                <div class="relative group bg-gray-50 dark:bg-gray-700 p-8 rounded-xl shadow-lg border border-gray-200 dark:border-gray-600 hover:shadow-xl transform hover:-translate-y-2 transition-all duration-300">
                    <div class="text-center mb-4">
                        <svg class="h-16 w-16 text-blue-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m14-6h2m-2 6h2M15 9h-1.5a2.5 2.5 0 01-2.5-2.5V7a2.5 2.5 0 00-2.5-2.5H9a2.5 2.5 0 00-2.5 2.5v1.5M12 10a2.5 2.5 0 00-2.5 2.5V14a2.5 2.5 0 01-2.5 2.5H7a2.5 2.5 0 01-2.5-2.5V10A2.5 2.5 0 007 7.5h1.5A2.5 2.5 0 0011 5h2a2.5 2.5 0 002.5 2.5h1.5A2.5 2.5 0 0117 10v1.5a2.5 2.5 0 002.5 2.5H19a2.5 2.5 0 002.5-2.5V10A2.5 2.5 0 0119 7.5h-1.5A2.5 2.5 0 0015 5h-2.5a2.5 2.5 0 00-2.5 2.5V10a2.5 2.5 0 00-2.5 2.5V14a2.5 2.5 0 01-2.5 2.5H7a2.5 2.5 0 01-2.5-2.5V10a2.5 2.5 0 00-2.5 2.5V14a2.5 2.5 0 01-2.5 2.5H3"></path></svg>
                        <h3 class="text-2xl font-semibold text-gray-800 dark:text-white">SaaS Sales Mastery</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 text-center">
                        Learn the art of closing high-value SaaS deals and master the sales funnel.
                    </p>
                    <!-- Hover Popup -->
                    <div class="absolute inset-0 bg-blue-600 text-white p-8 rounded-xl flex items-center justify-center opacity-0 group-hover:opacity-95 transition-opacity duration-300">
                        <p class="text-lg text-center">Comprehensive training in SaaS sales methodologies, lead generation, CRM utilization, and negotiation techniques for success in the tech industry.</p>
                    </div>
                </div>

                <!-- Course Card 2 -->
                <div class="relative group bg-gray-50 dark:bg-gray-700 p-8 rounded-xl shadow-lg border border-gray-200 dark:border-gray-600 hover:shadow-xl transform hover:-translate-y-2 transition-all duration-300">
                    <div class="text-center mb-4">
                        <svg class="h-16 w-16 text-blue-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c1.657 0 3 .895 3 2s-1.343 2-3 2-3-.895-3-2 1.343-2 3-2z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 16c-1.657 0-3-.895-3-2s1.343-2 3-2 3 .895 3 2-1.343 2-3 2z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 20c-1.657 0-3-.895-3-2s1.343-2 3-2 3 .895 3 2-1.343 2-3 2z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4c-1.657 0-3-.895-3-2s1.343-2 3-2 3 .895 3 2-1.343 2-3 2z"></path></svg>
                        <h3 class="text-2xl font-semibold text-gray-800 dark:text-white">Product Management</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 text-center">
                        Become a product leader, defining and launching successful SaaS products.
                    </p>
                    <!-- Hover Popup -->
                    <div class="absolute inset-0 bg-blue-600 text-white p-8 rounded-xl flex items-center justify-center opacity-0 group-hover:opacity-95 transition-opacity duration-300">
                        <p class="text-lg text-center">Master the product lifecycle from ideation to launch, market research, agile methodologies, and effective stakeholder communication in the SaaS ecosystem.</p>
                    </div>
                </div>

                <!-- Course Card 3 -->
                <div class="relative group bg-gray-50 dark:bg-gray-700 p-8 rounded-xl shadow-lg border border-gray-200 dark:border-gray-600 hover:shadow-xl transform hover:-translate-y-2 transition-all duration-300">
                    <div class="text-center mb-4">
                        <svg class="h-16 w-16 text-blue-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path></svg>
                        <h3 class="text-2xl font-semibold text-gray-800 dark:text-white">SaaS Marketing Growth</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 text-center">
                        Drive user acquisition and retention with cutting-edge SaaS marketing strategies.
                    </p>
                    <!-- Hover Popup -->
                    <div class="absolute inset-0 bg-blue-600 text-white p-8 rounded-xl flex items-center justify-center opacity-0 group-hover:opacity-95 transition-opacity duration-300">
                        <p class="text-lg text-center">Explore digital marketing channels, content strategy, SEO, SEM, social media marketing, and analytics tailored for SaaS businesses to achieve rapid growth.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section id="why-choose-us" class="py-16 bg-blue-50 dark:bg-gray-900 transition-colors duration-300 rounded-3xl shadow-lg m-6">
        <div class="container mx-auto px-6">
            <h2 class="text-4xl font-bold text-center mb-12 text-gray-800 dark:text-white">Why Choose Us?</h2>
            <div class="flex flex-col lg:flex-row items-center gap-12">
                <div class="lg:w-1/2">
                    <ul class="space-y-6 text-lg text-gray-700 dark:text-gray-300">
                        <li class="flex items-start">
                            <svg class="h-6 w-6 text-blue-600 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                            <strong>Industry Expert Faculty:</strong> Learn from seasoned professionals with real-world SaaS experience.
                        </li>
                        <li class="flex items-start">
                            <svg class="h-6 w-6 text-blue-600 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg>
                            <strong>Practical, Hands-on Training:</strong> Gain practical skills through live projects and case studies.
                        </li>
                        <li class="flex items-start">
                            <svg class="h-6 w-6 text-blue-600 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>
                            <strong>Career Placement Support:</strong> We assist you in landing your dream job in top SaaS companies.
                        </li>
                        <li class="flex items-start">
                            <svg class="h-6 w-6 text-blue-600 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>
                            <strong>Global Network:</strong> Connect with a vast network of SaaS professionals and alumni worldwide.
                        </li>
                    </ul>
                </div>
                <div class="lg:w-1/2 w-full flex items-center justify-center p-4">
                    <div class="w-full max-w-md bg-white dark:bg-gray-700 rounded-xl shadow-lg p-6">
                        <h3 class="text-xl font-semibold text-center mb-6 text-gray-800 dark:text-white">Our Success Metrics</h3>
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <span class="w-24 text-gray-700 dark:text-gray-300">Placement Rate:</span>
                                <div class="flex-grow bg-gray-200 dark:bg-gray-600 rounded-full h-6 overflow-hidden">
                                    <div class="chart-bar bg-green-500 rounded-full h-full flex items-center justify-end pr-2 text-white font-bold" style="--bar-width: 90%;">90%</div>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <span class="w-24 text-gray-700 dark:text-gray-300">Student Satisfaction:</span>
                                <div class="flex-grow bg-gray-200 dark:bg-gray-600 rounded-full h-6 overflow-hidden">
                                    <div class="chart-bar bg-blue-500 rounded-full h-full flex items-center justify-end pr-2 text-white font-bold" style="--bar-width: 95%;">95%</div>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <span class="w-24 text-gray-700 dark:text-gray-300">Industry Partnerships:</span>
                                <div class="flex-grow bg-gray-200 dark:bg-gray-600 rounded-full h-6 overflow-hidden">
                                    <div class="chart-bar bg-purple-500 rounded-full h-full flex items-center justify-end pr-2 text-white font-bold" style="--bar-width: 80%;">80+</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Faculty Section -->
    <section id="faculty" class="py-16 bg-white dark:bg-gray-800 transition-colors duration-300 rounded-3xl shadow-lg m-6">
        <div class="container mx-auto px-6">
            <h2 class="text-4xl font-bold text-center mb-12 text-gray-800 dark:text-white">Meet Our Expert Faculty</h2>
            <div class="relative max-w-3xl mx-auto">
                <div id="faculty-slider" class="relative overflow-hidden rounded-xl shadow-lg">
                    <div id="slider-content" class="flex transition-transform duration-500 ease-in-out">
                        <!-- Faculty Member 1 -->
                        <div class="w-full flex-shrink-0 p-8 flex flex-col md:flex-row items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-xl">
                            <img src="https://placehold.co/150x150/d1d5db/4b5563?text=John+Doe" alt="John Doe" class="w-36 h-36 rounded-full object-cover shadow-md mb-6 md:mb-0 md:mr-8">
                            <div class="text-center md:text-left">
                                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">John Doe</h3>
                                <p class="text-blue-600 text-lg mb-2">Lead SaaS Sales Trainer</p>
                                <p class="text-gray-600 dark:text-gray-300">With 15+ years in enterprise SaaS sales, John has closed deals worth millions and trained hundreds of successful sales professionals.</p>
                            </div>
                        </div>
                        <!-- Faculty Member 2 -->
                        <div class="w-full flex-shrink-0 p-8 flex flex-col md:flex-row items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-xl">
                            <img src="https://placehold.co/150x150/d1d5db/4b5563?text=Jane+Smith" alt="Jane Smith" class="w-36 h-36 rounded-full object-cover shadow-md mb-6 md:mb-0 md:mr-8">
                            <div class="text-center md:text-left">
                                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">Jane Smith</h3>
                                <p class="text-blue-600 text-lg mb-2">Head of Product Management</p>
                                <p class="text-gray-600 dark:text-gray-300">Jane has led product teams at leading tech giants, bringing innovative SaaS solutions to market.</p>
                            </div>
                        </div>
                        <!-- Faculty Member 3 -->
                        <div class="w-full flex-shrink-0 p-8 flex flex-col md:flex-row items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-xl">
                            <img src="https://placehold.co/150x150/d1d5db/4b5563?text=Emily+Clark" alt="Emily Clark" class="w-36 h-36 rounded-full object-cover shadow-md mb-6 md:mb-0 md:mr-8">
                            <div class="text-center md:text-left">
                                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">Emily Clark</h3>
                                <p class="text-blue-600 text-lg mb-2">SaaS Marketing Strategist</p>
                                <p class="text-gray-600 dark:text-gray-300">Emily specializes in growth hacking and building strong brand presence for SaaS startups.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Slider Navigation -->
                <button id="prevBtn" class="absolute left-0 top-1/2 -translate-y-1/2 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75 transform -translate-x-1/2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="15 18 9 12 15 6"></polyline></svg>
                </button>
                <button id="nextBtn" class="absolute right-0 top-1/2 -translate-y-1/2 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75 transform translate-x-1/2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>
                </button>
            </div>
        </div>
    </section>

    <!-- Lead Form Section -->
    <section id="lead-form" class="py-16 bg-blue-600 text-white rounded-3xl shadow-lg m-6">
        <div class="container mx-auto px-6">
            <h2 class="text-4xl font-bold text-center mb-12">Get a Free Demo Class!</h2>
            <div class="max-w-xl mx-auto bg-blue-700 p-8 rounded-xl shadow-lg">
                <form class="space-y-6">
                    <div>
                        <label for="name" class="block text-lg font-medium mb-2">Full Name</label>
                        <input type="text" id="name" name="name" placeholder="John Doe" class="w-full px-4 py-3 rounded-lg bg-blue-800 border border-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-400 dark:bg-blue-900 dark:border-blue-800" required>
                    </div>
                    <div>
                        <label for="email" class="block text-lg font-medium mb-2">Email Address</label>
                        <input type="email" id="email" name="email" placeholder="<EMAIL>" class="w-full px-4 py-3 rounded-lg bg-blue-800 border border-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-400 dark:bg-blue-900 dark:border-blue-800" required>
                    </div>
                    <div>
                        <label for="phone" class="block text-lg font-medium mb-2">Phone Number</label>
                        <input type="tel" id="phone" name="phone" placeholder="+****************" class="w-full px-4 py-3 rounded-lg bg-blue-800 border border-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-400 dark:bg-blue-900 dark:border-blue-800">
                    </div>
                    <div>
                        <label for="message" class="block text-lg font-medium mb-2">Your Message (Optional)</label>
                        <textarea id="message" name="message" rows="4" placeholder="Tell us about your career goals..." class="w-full px-4 py-3 rounded-lg bg-blue-800 border border-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-400 dark:bg-blue-900 dark:border-blue-800"></textarea>
                    </div>
                    <button type="submit" class="w-full bg-white text-blue-800 font-bold py-3 px-6 rounded-lg shadow-md hover:bg-gray-100 transform hover:scale-105 transition-all duration-300">
                        Request Demo
                    </button>
                </form>
            </div>
        </div>
    </section>

    <footer class="py-8 text-center text-gray-600 dark:text-gray-400 bg-white dark:bg-gray-800 transition-colors duration-300 rounded-t-3xl shadow-inner m-6 mt-0">
        <p>&copy; 2025 SaaSnext Academy. All rights reserved.</p>
    </footer>

    <script>
        // Theme Toggle
        const themeToggleBtn = document.getElementById('themeToggle');
        const htmlElement = document.documentElement;

        themeToggleBtn.addEventListener('click', () => {
            htmlElement.classList.toggle('dark');
            // Store user's preference in local storage
            if (htmlElement.classList.contains('dark')) {
                localStorage.setItem('theme', 'dark');
            } else {
                localStorage.setItem('theme', 'light');
            }
        });

        // Apply theme on load
        window.addEventListener('DOMContentLoaded', () => {
            if (localStorage.getItem('theme') === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                htmlElement.classList.add('dark');
            } else {
                htmlElement.classList.remove('dark');
            }
        });

        // Typing Effect for Hero Section
        const typingTextElement = document.getElementById('typing-text');
        const phrases = ["We Build Careers", "Launch Your SaaS Journey", "Unlock Your Potential", "Master SaaS Skills"];
        let phraseIndex = 0;
        let charIndex = 0;
        let isDeleting = false;
        let typingSpeed = 150; // Milliseconds per character

        function typeWriter() {
            const currentPhrase = phrases[phraseIndex];
            if (isDeleting) {
                typingTextElement.textContent = currentPhrase.substring(0, charIndex - 1);
                charIndex--;
            } else {
                typingTextElement.textContent = currentPhrase.substring(0, charIndex + 1);
                charIndex++;
            }

            if (!isDeleting && charIndex === currentPhrase.length) {
                // Phrase typed, start deleting after a pause
                typingSpeed = 100; // Faster deletion
                isDeleting = true;
                setTimeout(typeWriter, 1500); // Pause before deleting
            } else if (isDeleting && charIndex === 0) {
                // Phrase deleted, move to next phrase
                isDeleting = false;
                phraseIndex = (phraseIndex + 1) % phrases.length;
                typingSpeed = 150; // Normal typing speed
                setTimeout(typeWriter, 500); // Pause before typing new phrase
            } else {
                setTimeout(typeWriter, typingSpeed);
            }
        }
        typeWriter(); // Start the typing effect

        // Faculty Slider
        const sliderContent = document.getElementById('slider-content');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        let currentSlide = 0;

        function updateSlider() {
            // Get the width of a single slide (including padding/margin if any, but flex-shrink-0 helps here)
            const slideWidth = sliderContent.children[0].offsetWidth;
            sliderContent.style.transform = `translateX(-${currentSlide * slideWidth}px)`;
        }

        prevBtn.addEventListener('click', () => {
            currentSlide = (currentSlide - 1 + sliderContent.children.length) % sliderContent.children.length;
            updateSlider();
        });

        nextBtn.addEventListener('click', () => {
            currentSlide = (currentSlide + 1) % sliderContent.children.length;
            updateSlider();
        });

        // Update slider on window resize
        window.addEventListener('resize', updateSlider);
        // Initial update to position correctly
        updateSlider();

        // Optional: Auto-slide
        let autoSlideInterval = setInterval(() => {
            currentSlide = (currentSlide + 1) % sliderContent.children.length;
            updateSlider();
        }, 5000); // Change slide every 5 seconds

        // Pause auto-slide on hover
        sliderContent.parentNode.addEventListener('mouseenter', () => clearInterval(autoSlideInterval));
        sliderContent.parentNode.addEventListener('mouseleave', () => {
            autoSlideInterval = setInterval(() => {
                currentSlide = (currentSlide + 1) % sliderContent.children.length;
                updateSlider();
            }, 5000);
        });

        // Animate chart bars when section comes into view
        const whyChooseUsSection = document.getElementById('why-choose-us');
        const chartBars = document.querySelectorAll('.chart-bar');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    chartBars.forEach(bar => {
                        bar.classList.add('animate');
                    });
                    observer.unobserve(entry.target); // Stop observing once animated
                }
            });
        }, { threshold: 0.5 }); // Trigger when 50% of the section is visible

        observer.observe(whyChooseUsSection);

        // Handle form submission (client-side only for this example)
        document.querySelector('#lead-form form').addEventListener('submit', function(event) {
            event.preventDefault(); // Prevent default form submission

            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const phone = document.getElementById('phone').value;
            const message = document.getElementById('message').value;

            // In a real application, you would send this data to a backend server.
            // For now, we'll just log it to the console and provide a confirmation message.
            console.log("Demo Class Request Submitted:");
            console.log("Name:", name);
            console.log("Email:", email);
            console.log("Phone:", phone);
            console.log("Message:", message);

            // Display a simple confirmation message to the user
            const formContainer = this.parentNode;
            formContainer.innerHTML = `
                <div class="text-center p-8 bg-green-500 rounded-xl shadow-lg">
                    <h3 class="text-3xl font-bold mb-4">Thank You!</h3>
                    <p class="text-xl">Your demo class request has been submitted successfully.</p>
                    <p class="mt-4">We will contact you shortly at ${email}.</p>
                </div>
            `;
        });

    </script>
</body>
</html>
