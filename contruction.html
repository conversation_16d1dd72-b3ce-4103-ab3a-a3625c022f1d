<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SaaSnext Constructions</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc; /* Light gray background */
            color: #334155; /* Dark slate text */
            overflow-x: hidden; /* Prevent horizontal scroll */
        }
        .hero-section {
            background-image: url('https://placehold.co/1920x800/2B6CB0/FFFFFF?text=Cityscape+Background'); /* Placeholder cityscape image */
            background-size: cover;
            background-position: center;
            position: relative;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            overflow: hidden;
        }
        .hero-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to right, rgba(29, 78, 216, 0.7), rgba(13, 148, 136, 0.7)); /* Blue to Teal gradient overlay */
            z-index: 1;
        }
        .headline {
            z-index: 2;
            animation: fadeInScale 2s ease-out forwards;
        }

        /* Keyframes for headline animation */
        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.8) translateY(20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        /* Reveal on scroll animation */
        .reveal-element {
            opacity: 0;
            transform: translateY(50px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .reveal-element.is-visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Project card hover effect */
        .project-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .project-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        /* Custom scrollbar for better aesthetics */
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb {
            background: #1e3a8a; /* Darker blue */
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #1e40af; /* Even darker blue */
        }

        /* Hamburger menu styling */
        .hamburger {
            display: none; /* Hidden by default on larger screens */
            cursor: pointer;
            flex-direction: column;
            justify-content: space-between;
            width: 30px;
            height: 20px;
            position: relative;
            z-index: 30; /* Above header background */
        }
        .hamburger span {
            display: block;
            width: 100%;
            height: 3px;
            background-color: white;
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        /* Hamburger animation for open/close */
        .hamburger.open span:nth-child(1) {
            transform: translateY(8px) rotate(45deg);
        }
        .hamburger.open span:nth-child(2) {
            opacity: 0;
        }
        .hamburger.open span:nth-child(3) {
            transform: translateY(-9px) rotate(-45deg);
        }

        /* Mobile navigation menu styling */
        .mobile-nav-menu {
            display: none; /* Hidden by default */
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh; /* Full viewport height */
            background-color: rgba(23, 37, 84, 0.95); /* Dark blue with transparency */
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 20;
            transition: transform 0.3s ease-out;
            transform: translateX(100%); /* Start off-screen to the right */
        }
        .mobile-nav-menu.open {
            display: flex; /* Show when open */
            transform: translateX(0); /* Slide into view */
        }
        .mobile-nav-menu a {
            padding: 1rem 0;
            font-size: 1.8rem;
            color: white;
            transition: color 0.2s ease;
        }
        .mobile-nav-menu a:hover {
            color: #14b8a6; /* Teal hover */
        }


        @media (max-width: 767px) {
            .hamburger {
                display: flex; /* Show hamburger on small screens */
            }
            .main-nav-links {
                display: none; /* Hide desktop nav on small screens */
            }
        }
    </style>
</head>
<body>

    <!-- Header Section -->
    <header class="bg-blue-900 text-white p-4 fixed w-full z-50 shadow-lg">
        <div class="container mx-auto flex justify-between items-center max-w-6xl">
            <!-- Logo/Site Title -->
            <a href="#hero" class="text-2xl font-bold text-teal-400 hover:text-teal-300 transition-colors">SaaSnext Constructions</a>

            <!-- Desktop Navigation Links -->
            <nav class="main-nav-links hidden md:flex space-x-8">
                <a href="#services" class="hover:text-teal-300 transition-colors">Services</a>
                <a href="#projects" class="hover:text-teal-300 transition-colors">Projects</a>
                <a href="#why-us" class="hover:text-teal-300 transition-colors">Why Us</a>
                <a href="#contact" class="hover:text-teal-300 transition-colors">Contact</a>
            </nav>

            <!-- Hamburger Icon for Mobile -->
            <div class="hamburger md:hidden" id="hamburger-icon">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <!-- Mobile Navigation Menu (hidden by default) -->
        <nav class="mobile-nav-menu" id="mobile-nav-menu">
            <a href="#services" class="mobile-nav-link" data-target="services">Services</a>
            <a href="#projects" class="mobile-nav-link" data-target="projects">Projects</a>
            <a href="#why-us" class="mobile-nav-link" data-target="why-us">Why Us</a>
            <a href="#contact" class="mobile-nav-link" data-target="contact">Contact</a>
        </nav>
    </header>

    <!-- Hero Section -->
    <section id="hero" class="hero-section">
        <div class="hero-overlay"></div>
        <div class="relative z-20 p-8 max-w-4xl mx-auto">
            <h1 class="headline text-5xl md:text-7xl font-extrabold leading-tight tracking-tight mb-4 drop-shadow-lg">
                Building Tomorrow's Skylines, Today.
            </h1>
            <p class="text-xl md:text-2xl font-light drop-shadow-md">
                Your trusted partner in innovative and sustainable construction solutions.
            </p>
        </div>
    </section>

    <!-- Our Services Section -->
    <section id="services" class="py-16 bg-white reveal-element">
        <div class="container mx-auto px-6 max-w-6xl">
            <h2 class="text-4xl font-bold text-center mb-12 text-blue-800">Our Services</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Service Card 1 -->
                <div class="bg-gray-50 p-8 rounded-xl shadow-md flex flex-col items-center text-center reveal-element">
                    <div class="text-5xl text-teal-600 mb-4">🏗️</div>
                    <h3 class="text-2xl font-semibold mb-3 text-blue-700">Structural Engineering</h3>
                    <p class="text-gray-600">Expert analysis, design, and optimization for robust and safe building structures.</p>
                </div>
                <!-- Service Card 2 -->
                <div class="bg-gray-50 p-8 rounded-xl shadow-md flex flex-col items-center text-center reveal-element">
                    <div class="text-5xl text-teal-600 mb-4">🔑</div>
                    <h3 class="text-2xl font-semibold mb-3 text-blue-700">Turnkey Solutions</h3>
                    <p class="text-gray-600">Comprehensive project management from concept to completion, ensuring seamless delivery.</p>
                </div>
                <!-- Service Card 3 -->
                <div class="bg-gray-50 p-8 rounded-xl shadow-md flex flex-col items-center text-center reveal-element">
                    <div class="text-5xl text-teal-600 mb-4">📐</div>
                    <h3 class="text-2xl font-semibold mb-3 text-blue-700">Architectural Design</h3>
                    <p class="text-gray-600">Creative and functional designs that bring your vision to life with aesthetic appeal.</p>
                </div>
                <!-- Service Card 4 -->
                <div class="bg-gray-50 p-8 rounded-xl shadow-md flex flex-col items-center text-center reveal-element">
                    <div class="text-5xl text-teal-600 mb-4">🌿</div>
                    <h3 class="text-2xl font-semibold mb-3 text-blue-700">Sustainable Construction</h3>
                    <p class="text-gray-600">Eco-friendly practices and materials for environmentally responsible projects.</p>
                </div>
                <!-- Service Card 5 -->
                <div class="bg-gray-50 p-8 rounded-xl shadow-md flex flex-col items-center text-center reveal-element">
                    <div class="text-5xl text-teal-600 mb-4">💡</div>
                    <h3 class="text-2xl font-semibold mb-3 text-blue-700">Renovation & Remodeling</h3>
                    <p class="text-gray-600">Transforming existing spaces with modern updates and improved functionality.</p>
                </div>
                <!-- Service Card 6 -->
                <div class="bg-gray-50 p-8 rounded-xl shadow-md flex flex-col items-center text-center reveal-element">
                    <div class="text-5xl text-teal-600 mb-4">🛡️</div>
                    <h3 class="text-2xl font-semibold mb-3 text-blue-700">Quality Assurance</h3>
                    <p class="text-gray-600">Rigorous quality checks at every stage to ensure excellence and durability.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Showcase Section -->
    <section id="projects" class="py-16 bg-gray-100 reveal-element">
        <div class="container mx-auto px-6 max-w-6xl">
            <h2 class="text-4xl font-bold text-center mb-8 text-blue-800">Our Projects</h2>
            <div class="flex justify-center mb-8 space-x-4 flex-wrap gap-2">
                <button class="filter-btn bg-blue-700 text-white px-6 py-2 rounded-lg hover:bg-blue-800 transition-colors shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50" data-filter="all">All</button>
                <button class="filter-btn bg-teal-600 text-white px-6 py-2 rounded-lg hover:bg-teal-700 transition-colors shadow-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-opacity-50" data-filter="residential">Residential</button>
                <button class="filter-btn bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50" data-filter="commercial">Commercial</button>
                <button class="filter-btn bg-teal-500 text-white px-6 py-2 rounded-lg hover:bg-teal-600 transition-colors shadow-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-opacity-50" data-filter="industrial">Industrial</button>
            </div>

            <div id="project-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Project Card 1 -->
                <div class="project-card bg-white rounded-xl shadow-lg overflow-hidden reveal-element" data-category="residential">
                    <img src="https://placehold.co/600x400/1e3a8a/FFFFFF?text=Luxury+Apartments" alt="Luxury Apartments" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 text-blue-800">Luxury Apartments</h3>
                        <p class="text-gray-600 text-sm">Modern residential complex with state-of-the-art amenities.</p>
                    </div>
                </div>
                <!-- Project Card 2 -->
                <div class="project-card bg-white rounded-xl shadow-lg overflow-hidden reveal-element" data-category="commercial">
                    <img src="https://placehold.co/600x400/0f766e/FFFFFF?text=Corporate+Tower" alt="Corporate Tower" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 text-blue-800">Corporate Tower</h3>
                        <p class="text-gray-600 text-sm">Sleek commercial building designed for modern businesses.</p>
                    </div>
                </div>
                <!-- Project Card 3 -->
                <div class="project-card bg-white rounded-xl shadow-lg overflow-hidden reveal-element" data-category="industrial">
                    <img src="https://placehold.co/600x400/1e3a8a/FFFFFF?text=Manufacturing+Plant" alt="Manufacturing Plant" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 text-blue-800">Manufacturing Plant</h3>
                        <p class="text-gray-600 text-sm">Advanced facility for efficient industrial operations.</p>
                    </div>
                </div>
                <!-- Project Card 4 -->
                <div class="project-card bg-white rounded-xl shadow-lg overflow-hidden reveal-element" data-category="residential">
                    <img src="https://placehold.co/600x400/0f766e/FFFFFF?text=Family+Homes" alt="Family Homes" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 text-blue-800">Sustainable Family Homes</h3>
                        <p class="text-gray-600 text-sm">Energy-efficient homes with modern designs and green features.</p>
                    </div>
                </div>
                <!-- Project Card 5 -->
                <div class="project-card bg-white rounded-xl shadow-lg overflow-hidden reveal-element" data-category="commercial">
                    <img src="https://placehold.co/600x400/1e3a8a/FFFFFF?text=Retail+Complex" alt="Retail Complex" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 text-blue-800">Retail Complex</h3>
                        <p class="text-gray-600 text-sm">Vibrant shopping destination with diverse retail spaces.</p>
                    </div>
                </div>
                <!-- Project Card 6 -->
                <div class="project-card bg-white rounded-xl shadow-lg overflow-hidden reveal-element" data-category="residential">
                    <img src="https://placehold.co/600x400/0f766e/FFFFFF?text=Urban+Villas" alt="Urban Villas" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2 text-blue-800">Urban Villas</h3>
                        <p class="text-gray-600 text-sm">Spacious villas offering luxury and comfort in urban settings.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Us Section -->
    <section id="why-us" class="py-16 bg-blue-800 text-white reveal-element">
        <div class="container mx-auto px-6 max-w-6xl">
            <h2 class="text-4xl font-bold text-center mb-12">Why Choose SaaSnext?</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center mb-12">
                <!-- Stat Card 1 -->
                <div class="p-6 bg-blue-700 rounded-xl shadow-md reveal-element">
                    <div class="text-5xl font-bold mb-2 count-up" data-target="15">0</div>
                    <p class="text-xl">Years of Experience</p>
                </div>
                <!-- Stat Card 2 -->
                <div class="p-6 bg-blue-700 rounded-xl shadow-md reveal-element">
                    <div class="text-5xl font-bold mb-2 count-up" data-target="250">0</div>
                    <p class="text-xl">Completed Projects</p>
                </div>
                <!-- Stat Card 3 -->
                <div class="p-6 bg-blue-700 rounded-xl shadow-md reveal-element">
                    <div class="text-5xl font-bold mb-2 count-up" data-target="100">0</div>
                    <p class="text-xl">Client Satisfaction (%)</p>
                </div>
            </div>

            <h3 class="text-3xl font-semibold text-center mb-8">What Our Clients Say</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- Testimonial Card 1 -->
                <div class="bg-blue-700 p-6 rounded-xl shadow-md reveal-element">
                    <p class="text-lg italic mb-4">"SaaSnext Constructions delivered our project on time and within budget, exceeding our expectations. Their attention to detail and commitment to quality are unparalleled."</p>
                    <p class="font-semibold">- Jane Doe, CEO of Urban Developments</p>
                </div>
                <!-- Testimonial Card 2 -->
                <div class="bg-blue-700 p-6 rounded-xl shadow-md reveal-element">
                    <p class="text-lg italic mb-4">"We are incredibly impressed with the structural integrity and aesthetic appeal of our new corporate headquarters. SaaSnext's team is truly professional."</p>
                    <p class="font-semibold">- John Smith, Founder of Tech Innovations</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-16 bg-gray-50 reveal-element">
        <div class="container mx-auto px-6 max-w-xl">
            <h2 class="text-4xl font-bold text-center mb-12 text-blue-800">Get in Touch</h2>
            <form action="#" method="POST" class="bg-white p-8 rounded-xl shadow-lg reveal-element">
                <div class="mb-6">
                    <label for="name" class="block text-gray-700 text-sm font-bold mb-2">Name</label>
                    <input type="text" id="name" name="name" class="shadow appearance-none border rounded-lg w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Your Name" required>
                </div>
                <div class="mb-6">
                    <label for="email" class="block text-gray-700 text-sm font-bold mb-2">Email</label>
                    <input type="email" id="email" name="email" class="shadow appearance-none border rounded-lg w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="<EMAIL>" required>
                </div>
                <div class="mb-6">
                    <label for="message" class="block text-gray-700 text-sm font-bold mb-2">Message</label>
                    <textarea id="message" name="message" rows="6" class="shadow appearance-none border rounded-lg w-full py-3 px-4 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-y" placeholder="Your message..." required></textarea>
                </div>
                <div class="flex items-center justify-center">
                    <button type="submit" class="bg-blue-700 hover:bg-blue-800 text-white font-bold py-3 px-8 rounded-lg transition-colors shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                        Send Message
                    </button>
                </div>
            </form>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-8 text-center reveal-element">
        <div class="container mx-auto px-6 max-w-6xl">
            <p>&copy; 2024 SaaSnext Constructions. All rights reserved.</p>
            <p class="mt-2 text-sm">Designed with precision and built with passion.</p>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Smooth scrolling for navigation
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                    // Close mobile menu if open
                    const mobileNavMenu = document.getElementById('mobile-nav-menu');
                    const hamburgerIcon = document.getElementById('hamburger-icon');
                    if (mobileNavMenu.classList.contains('open')) {
                        mobileNavMenu.classList.remove('open');
                        hamburgerIcon.classList.remove('open');
                    }
                });
            });

            // Hamburger menu toggle for mobile
            const hamburgerIcon = document.getElementById('hamburger-icon');
            const mobileNavMenu = document.getElementById('mobile-nav-menu');

            hamburgerIcon.addEventListener('click', function() {
                this.classList.toggle('open');
                mobileNavMenu.classList.toggle('open');
            });


            // Project Filtering Logic
            const filterButtons = document.querySelectorAll('.filter-btn');
            const projectGrid = document.getElementById('project-grid');
            const projectCards = projectGrid.querySelectorAll('.project-card');

            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const filter = this.dataset.filter;

                    // Remove active state from all buttons and add to clicked button
                    filterButtons.forEach(btn => btn.classList.remove('bg-blue-900', 'hover:bg-blue-900', 'bg-teal-700', 'hover:bg-teal-700', 'bg-blue-700', 'hover:bg-blue-800', 'bg-teal-600', 'hover:bg-teal-700', 'bg-teal-500', 'hover:bg-teal-600'));
                    filterButtons.forEach(btn => {
                        if (btn.dataset.filter === 'all') btn.classList.add('bg-blue-700', 'hover:bg-blue-800');
                        else if (btn.dataset.filter === 'residential') btn.classList.add('bg-teal-600', 'hover:bg-teal-700');
                        else if (btn.dataset.filter === 'commercial') btn.classList.add('bg-blue-600', 'hover:bg-blue-700');
                        else if (btn.dataset.filter === 'industrial') btn.classList.add('bg-teal-500', 'hover:bg-teal-600');
                    });
                    this.classList.add('bg-blue-900', 'hover:bg-blue-900'); // Active state color

                    projectCards.forEach(card => {
                        const category = card.dataset.category;
                        if (filter === 'all' || category === filter) {
                            card.style.display = 'block'; // Show card
                            setTimeout(() => card.classList.add('is-visible'), 100); // Trigger reveal animation
                        } else {
                            card.classList.remove('is-visible'); // Hide animation
                            card.style.display = 'none'; // Hide card after animation (or immediately)
                        }
                    });
                });
            });

            // Trigger "All" filter on initial load to show all projects
            document.querySelector('.filter-btn[data-filter="all"]').click();


            // Count-up Stats Animation
            const countUpElements = document.querySelectorAll('.count-up');

            function animateCountUp(element) {
                const target = parseInt(element.dataset.target);
                let current = 0;
                const duration = 2000; // 2 seconds
                const increment = target / (duration / 10); // Calculate increment for smooth animation

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    element.textContent = Math.floor(current);
                }, 10);
            }

            // Reveal on Scroll Animation (Intersection Observer)
            const revealElements = document.querySelectorAll('.reveal-element');

            const observer = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('is-visible');
                        // If it's a count-up element, trigger the animation
                        if (entry.target.classList.contains('count-up')) {
                            animateCountUp(entry.target);
                        }
                        // Stop observing once visible (optional, for one-time animation)
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1, // Trigger when 10% of the element is visible
                rootMargin: '0px 0px -50px 0px' // Start animation 50px before it fully enters viewport
            });

            revealElements.forEach(element => {
                observer.observe(element);
            });
        });
    </script>
</body>
</html>
